#!/usr/bin/env node
import fg from "fast-glob";
import fs from "fs/promises";

// example: lib_version=0.6.95 lib_name=sw-wallet-adapter-core npm run sync-lib
async function syncLibrary() {
    if (!process.env.lib_version || !process.env.lib_name) {
        return ;
    }
    const packageJsonPaths = await fg(["packages/*/package.json"]);
    for (const pkgPath of packageJsonPaths) {
        const pkgRaw = await fs.readFile(pkgPath, { encoding: "utf-8" });
        const pkg = JSON.parse(pkgRaw);

        if (pkg?.dependencies?.[`@skywind-group/${process.env.lib_name}`]) {
            console.log("Updated:", pkg.name);
            pkg.dependencies[`@skywind-group/${process.env.lib_name}`] = process.env.lib_version;

            await fs.writeFile(pkgPath, JSON.stringify(pkg, null, 2) + "\n");
        }
    }
    console.log("🎉 Synced library");
}

void syncLibrary().catch((err) => {
    console.error("❌ Failed to sync lib:", err);
    process.exit(1);
});
