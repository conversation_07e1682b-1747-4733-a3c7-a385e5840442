#!/usr/bin/env node
import fg from "fast-glob";
import fs from "fs/promises";

async function generateTs() {
    const tsCommonConfigRaw = await fs.readFile("./tsconfig.build.json", { encoding: "utf-8" });
    const tsCommonConfig = JSON.parse(tsCommonConfigRaw);

    const packageJsonPaths = await fg(["packages/*/package.json"]);
    for (const pkgPath of packageJsonPaths) {
        const pkgRaw = await fs.readFile(pkgPath, { encoding: "utf-8" });
        const pkg = JSON.parse(pkgRaw);
        if (pkg.private) {
            const tsPath = pkgPath.replace("package.json", "tsconfig.build.json");
            const tsConfigRaw = await fs.readFile(tsPath, { encoding: "utf-8" });
            const tsConfig = JSON.parse(tsConfigRaw);
            delete tsConfig.extends;
            tsConfig.compilerOptions = { ...tsCommonConfig.compilerOptions, ...tsConfig.compilerOptions };
            const config = { ...tsCommonConfig, ...tsConfig };

            await fs.writeFile(tsPath, JSON.stringify(config, null, 2) + "\n");
        }
    }
}

void generateTs();
