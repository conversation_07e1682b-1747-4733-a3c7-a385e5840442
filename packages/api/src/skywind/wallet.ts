import { Redis } from "ioredis";
import { IDirectWalletConfiguration, IWalletConfiguration, IHttpWalletConfiguration } from "@skywind-group/sw-wallet";
import { sequelize as db, sequelizeWalletArchive as archiveDB } from "../skywind/storage/db";
import config from "./config";
import { RemoteWalletFacade, WalletFacade } from "@skywind-group/sw-management-wallet";
import { redis } from "@skywind-group/sw-utils";

// create instance of the redisConnection
const walletRedisPool = redis.createRedisPool<Redis>(config.walletRedis);
const walletConfig: IDirectWalletConfiguration = {
    type: "direct",

    db: () => {
        return db;
    },

    archiveDB: () => {
        return archiveDB;
    },

    connection: () => {
        return walletRedisPool;
    },

    trxIdRange: config.trxIdRange,

    externalTransactionLog: config.walletExternalLog.on ? config.walletExternalLog.config : undefined
};

const httpWalletConfig: Partial<IHttpWalletConfiguration> = {
    type: "http",

    connection: () => {
        return walletRedisPool;
    },

    trxIdRange: config.trxIdRange,

    baseURL: config.walletConductor.baseURL,

    externalTransactionLog: config.walletExternalLog.on ? config.walletExternalLog.config : undefined
};

const walletConfigs = {
    [httpWalletConfig.type]: httpWalletConfig,
    [walletConfig.type]: walletConfig
};

export function getConfig(type: string): IWalletConfiguration {
    const result = walletConfigs[type];
    if (!result) {
        throw new Error(`Unsupported wallet conductor type '${type}'`);
    }

    return result;
}

WalletFacade.initWalletManager(getConfig(config.walletConductor.type));
RemoteWalletFacade.initWalletManager(getConfig("http"));
