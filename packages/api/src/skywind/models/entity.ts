import {
    Model,
    ModelStatic,
    DataTypes,
    InferAttributes,
    InferCreationAttributes,
    CreationOptional,
    NonAttribute,
    ForeignKey
} from "sequelize";
import { sequelize as db } from "../storage/db";
import {
    ENTITY_TYPE,
    EntityStatus,
    MIGRATION_STATUS,
    WEBSITE_WHITELISTED_CHECK_LEVEL
} from "../entities/entity";
import { getDynamicDomainModel, getStaticDomainModel, DynamicDomainModel, StaticDomainModel } from "./domain";
import { getDeploymentGroupModel, DeploymentGroupModel } from "./deploymentGroup";

const ddModel = getDynamicDomainModel();
const sdModel = getStaticDomainModel();
const dgModel = getDeploymentGroupModel();

export class EntityModel extends Model<
    InferAttributes<EntityModel, { omit: "parent" }>,
    InferCreationAttributes<EntityModel, { omit: "parent" }>
> {
    declare id: CreationOptional<number>;
    declare name: string;
    declare title: string;
    declare type: string;
    declare description: string;
    declare path: string;
    declare status: string;
    declare key: string;

    declare defaultCurrency: string;
    declare defaultCountry: string;
    declare defaultLanguage: string;

    declare countries: string[];
    declare currencies: string[];
    declare languages: string[];

    declare dynamicDomainId: ForeignKey<DynamicDomainModel["id"]>;
    declare environment: string;
    declare prevDynamicDomainId: ForeignKey<DynamicDomainModel["id"]>;
    declare migrationStatus: MIGRATION_STATUS;
    declare staticDomainId: ForeignKey<StaticDomainModel["id"]>;
    declare staticDomainTags: string[];
    declare domains: string[];

    declare staticDomainPoolId?: number;
    declare dynamicDomainPoolId?: number;

    declare isTest: boolean;
    declare version: number;

    declare merchantTypes: string[];
    declare deploymentGroupId: ForeignKey<DeploymentGroupModel["id"]>;

    declare createdAt: CreationOptional<Date>;
    declare updatedAt: CreationOptional<Date>;
    declare checkWebSiteWhitelisted: string;

    declare parent?: NonAttribute<EntityModel>;
}

const schema = {
    id: { type: DataTypes.INTEGER, autoIncrement: true, primaryKey: true },
    type: { type: DataTypes.ENUM(...Object.values(ENTITY_TYPE)), allowNull: false },
    name: { type: DataTypes.STRING, allowNull: false, validate: { is: /^[\w-]+$/, notEmpty: true } },
    title: { type: DataTypes.STRING, allowNull: true },
    description: DataTypes.STRING,
    status: {
        type: DataTypes.ENUM(...Object.values(EntityStatus)),
        allowNull: false
    },
    key: {
        type: DataTypes.STRING,
        defaultValue: DataTypes.UUIDV4,
        unique: true,
    },
    path: { type: DataTypes.STRING, unique: true, validate: { notEmpty: true } },
    defaultCurrency: {
        type: DataTypes.STRING,
        field: "default_currency",
        allowNull: false,
        validate: { notEmpty: true }
    },
    defaultCountry: {
        type: DataTypes.STRING,
        field: "default_country",
        allowNull: true
    },
    defaultLanguage: {
        type: DataTypes.STRING,
        field: "default_language",
        allowNull: false,
        validate: { notEmpty: true }
    },
    countries: { type: DataTypes.JSONB, allowNull: false },
    currencies: { type: DataTypes.JSONB, allowNull: false, validate: { notEmpty: true } },
    languages: { type: DataTypes.JSONB, allowNull: false, validate: { notEmpty: true } },
    dynamicDomainId: { type: DataTypes.INTEGER, allowNull: true, field: "dynamic_domain_id" },
    prevDynamicDomainId: { type: DataTypes.INTEGER, allowNull: true, field: "prev_dynamic_domain_id" },
    migrationStatus: {
        type: DataTypes.ENUM(...Object.values(MIGRATION_STATUS)),
        allowNull: true,
        field: "migration_status"
    },
    environment: { type: DataTypes.STRING, allowNull: true, field: "environment" },
    staticDomainId: { type: DataTypes.INTEGER, allowNull: true, field: "static_domain_id" },
    domains: { type: DataTypes.JSONB, allowNull: true },
    isTest: { type: DataTypes.BOOLEAN, allowNull: true, field: "is_test", defaultValue: false },
    merchantTypes: {
        type: DataTypes.JSONB,
        allowNull: true,
        field: "merchant_types"
    },
    version: DataTypes.INTEGER,
    createdAt: {
        type: DataTypes.DATE,
        field: "created_at",
    },
    updatedAt: {
        type: DataTypes.DATE,
        field: "updated_at",
    },
    staticDomainTags: {
        type: DataTypes.JSONB,
        allowNull: true,
        field: "static_domain_tags"
    },
    checkWebSiteWhitelisted: {
        type: DataTypes.ENUM(...Object.values(WEBSITE_WHITELISTED_CHECK_LEVEL)),
        allowNull: true,
        field: "check_website_whitelisted"
    },
    deploymentGroupId: {
        type: DataTypes.INTEGER,
        allowNull: true,
        field: "deployment_group_id"
    },
    staticDomainPoolId: {
        type: DataTypes.INTEGER,
        allowNull: true,
        field: "static_domain_pool_id"
    },
    dynamicDomainPoolId: {
        type: DataTypes.INTEGER,
        allowNull: true,
        field: "dynamic_domain_pool_id"
    }
};

export type IEntityModel = ModelStatic<EntityModel>;
const model = EntityModel.init(
    schema,
    {
        sequelize: db,
        modelName: "entity",
        tableName: "entities",
        underscored: true,
    }
);

model.belongsTo(model, { foreignKey: "parent", targetKey: "id", onDelete: "NO ACTION" });
model.belongsTo(ddModel, { foreignKey: "dynamicDomainId", targetKey: "id", onDelete: "NO ACTION" });
model.belongsTo(ddModel,
    { foreignKey: "prevDynamicDomainId", targetKey: "id", onDelete: "NO ACTION", as: "prevDynamicDomain" });
model.belongsTo(sdModel, { foreignKey: "staticDomainId", targetKey: "id", onDelete: "NO ACTION" });
model.belongsTo(dgModel, { foreignKey: "deploymentGroupId", targetKey: "id", onDelete: "NO ACTION" });

export function get(): IEntityModel {
    return model;
}
