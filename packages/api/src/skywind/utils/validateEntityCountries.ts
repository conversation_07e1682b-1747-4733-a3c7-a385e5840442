import { BaseEntity, ChildEntity, Entity } from "../entities/entity";
import * as Errors from "../errors";
import { COUNTRIES } from "../utils/common";
import { isEqual } from "lodash";
import { Jurisdiction } from "../entities/jurisdiction";
import { EntitySettings } from "../entities/settings";

interface UpdateData {
    defaultCountry?: string | null;
    countries?: Array<string>;
}

export function validateCreateDefaultCountry(
    { defaultCountry }: UpdateData,
    parentEntity: BaseEntity,
    jurisdiction: Jurisdiction,
    entitySettings: EntitySettings
) {
    if (defaultCountry) {
        validateCountryRestrictions(defaultCountry, parentEntity, jurisdiction, entitySettings, "Default country");
    } else {
        if (!jurisdiction.defaultCountry) {
            throw new Errors.ValidationError("Default country should be defined in jurisdiction or entity");
        }
    }
}

export function validateUpdateDefaultCountry(
    { defaultCountry }: UpdateData,
    parentEntity: BaseEntity,
    childEntity: ChildEntity,
    jurisdiction: Jurisdiction | undefined,
    entitySettings: EntitySettings
) {
    if (childEntity.defaultCountry !== defaultCountry) {
        if (defaultCountry === null) {
            if (!jurisdiction?.defaultCountry) {
                throw new Errors.ValidationError("Default country should be defined in jurisdiction or entity");
            }
        } else if (defaultCountry !== undefined) {
            validateCountryRestrictions(defaultCountry, parentEntity, jurisdiction, entitySettings, "Default country");
        }
    }
}

export function validateCreateCountries(
    data: UpdateData,
    parentEntity: BaseEntity,
    entitySettings: EntitySettings
) {
    if (entitySettings.useCountriesFromJurisdiction) {
        data.countries = null;
    }
    if (data.countries) {
        if (!Array.isArray(data.countries)) {
            throw new Errors.CountriesIsNotArray();
        }
        for (const country of data.countries) {
            validateCountryRestrictions(country, parentEntity, undefined, entitySettings, "Country");
        }
    }
}

export function validateUpdateCountries(
    data: UpdateData,
    parentEntity: BaseEntity,
    childEntity: ChildEntity,
    entitySettings: EntitySettings
) {
    if (entitySettings.useCountriesFromJurisdiction) {
        data.countries = undefined;
    }
    if (data.countries) {
        const currentCountries = childEntity.getCountries();
        if (!isEqual(currentCountries, data.countries)) {
            if (!Array.isArray(data.countries)) {
                throw new Errors.CountriesIsNotArray();
            }
            for (const country of data.countries) {
                validateCountryRestrictions(country, parentEntity, undefined, entitySettings, "Country");
            }

            const removedCountries = currentCountries.filter(code => !data.countries.includes(code));
            for (const code of removedCountries) {
                const children = ((childEntity as Entity).child || []).filter(child => child.countryExists(code));
                if (children.length) {
                    throw new Errors.ValidationError(
                        `This country code ${code} in use in child entities. You cannot remove it.`,
                        undefined,
                        {
                            children: children.map(child => ({
                                title: child.title,
                                path: child.path,
                                name: child.name
                            }))
                        });
                }
            }
        }
    }
}

export function validateCountryRestrictions(
    country: string | undefined | null,
    parentEntity: BaseEntity,
    jurisdiction: Jurisdiction | undefined,
    entitySettings: EntitySettings,
    name: string
) {
    if (!COUNTRIES[country]) {
        throw new Errors.ValidationError(`${name} [${country}] is invalid`);
    }
    if (entitySettings.useCountriesFromJurisdiction && jurisdiction) {
        const allowedCountries = jurisdiction.allowedCountries;
        if (allowedCountries?.length && !allowedCountries.includes(country)) {
            throw new Errors.ValidationError(`${name} [${country}] should be in jurisdiction allowed countries`);
        }
        const restrictedCountries = jurisdiction.restrictedCountries;
        if (restrictedCountries?.length && restrictedCountries.includes(country)) {
            throw new Errors.ValidationError(`${name} [${country}] cannot be in jurisdiction restricted countries`);
        }
    } else {
        const allowedCountries = parentEntity.getCountries();
        if (allowedCountries?.length && !allowedCountries.includes(country)) {
            throw new Errors.ValidationError(`${name} [${country}] should be in parent entity countries`);
        }
        const restrictedCountries = entitySettings.restrictedCountries;
        if (restrictedCountries?.length && restrictedCountries.includes(country)) {
            throw new Errors.ValidationError(`${name} [${country}] cannot be in entity settings restricted countries`);
        }
    }
}
