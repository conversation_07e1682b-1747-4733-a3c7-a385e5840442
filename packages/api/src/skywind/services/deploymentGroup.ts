import { lazy } from "@skywind-group/sw-utils";
import { FindOptions, Transaction, UpdateOptions, WhereOptions } from "sequelize";
import * as Errors from "../errors";
import { EntityGame, Game } from "../entities/game";
import EntityCache from "../cache/entity";
import { BaseEntity, EntityAttributes } from "../entities/entity";
import {
    GamesListWithHashes,
    GetGameCriticalFilesInfoRequest,
    HashCriticalFilesRequest,
    MerchantRegulation
} from "../entities/merchant";
import { findOneEntityGame } from "./game";
import { buildDynamicGSUrl } from "./entityDomainService";
import logger from "../utils/logger";
import { IncomingMessage } from "node:http";
import { BrandEntity } from "../entities/brand";
import { isSWGameServerError } from "../history/unfinishedRoundManagementService";
import { GameCodesWithVersions } from "./criticalfiles/criticalFilesService";
import config from "../config";
import { getGame } from "./gameprovider";
import { Models } from "../models/models";
import { URL } from "node:url";
import { DeploymentGroupAttributes, DeploymentGroupType } from "../entities/deploymentGroup";
import { DeploymentGroupModel } from "../models/deploymentGroup";
import request = require("request");

const GameModel = Models.GameModel;
const EntityModel = Models.EntityModel;
const dgModel = Models.DeploymentGroupModel;

const log = logger("round-management");

const DEPLOYMENT_ROUTE_SEPARATOR = "-";

export enum DeploymentGroupRoute {
    live = "live"
}

const JPN_GAMES_MODULE = "sw-jpn-games";
const ITG_GAMES_PREFIX = config.itgGameCodePrefix;

export interface DeploymentGroupService {

    getDeploymentGroup(id: number): Promise<DeploymentGroupAttributes>;

    getDeploymentGroupByRoute(route: string): Promise<DeploymentGroupAttributes>;

    buildDeploymentGroupPath(...ids: number[]): Promise<string>;

    getDeploymentGroups(): Promise<DeploymentGroupAttributes[]>;

    assignDeploymentGroupToGame(gameCode: string, route: string, transaction?: Transaction): Promise<void>;

    assignDeploymentGroupToEntity(entityPath: string, route: string): Promise<void>;

    addDeploymentGroup(route: string, type: DeploymentGroupType, description?: string): Promise<DeploymentGroupAttributes>;

    getAssignedEntities(route: string): Promise<string[]>;

    getAssignedGames(route: string): Promise<string[]>;

    unAssignDeploymentGroupFromGame(code: string): Promise<void>;

    unAssignDeploymentGroupFromEntity(path: string): Promise<void>;
}

export interface GameServerApiProvider {

    senPostByCtxId<T>(endpoint: string, req: any, entity: BrandEntity, contextId: string): Promise<T>;

    sendPostForForceFinish<T>(endpoint: string,
                              req: any,
                              entity: BrandEntity,
                              contextId: string,
                              gameCode?: string): Promise<T>;

    sendPostByGameCode<T>(endpoint: string, req: any, entity: BrandEntity, gameCode: string, playerCode: string): Promise<T>;

    sendPostByEntity<T>(endpoint: string, req: any, entity: BrandEntity, playerCode?: string): Promise<T>;
}

class CacheableDeploymentGroupService implements DeploymentGroupService {
    private readonly groupsCacheById = new Map<number, DeploymentGroupAttributes>();
    private readonly groupsCacheByRoute = new Map<string, DeploymentGroupAttributes>();

    public async getDeploymentGroup(id: number): Promise<DeploymentGroupAttributes> {
        const fromCache = this.groupsCacheById.get(id);
        if (fromCache) {
            return fromCache;
        }

        return this.loadDeploymentGroupById(id);
    }

    public async getDeploymentGroupByRoute(route: string): Promise<DeploymentGroupAttributes> {
        const fromCache = this.groupsCacheByRoute.get(route);
        if (fromCache) {
            return fromCache;
        }

        return this.loadDeploymentGroupByRoute(route);
    }

    public async addDeploymentGroup(route: string,
                                    type: DeploymentGroupType,
                                    description?: string): Promise<DeploymentGroupAttributes> {
        const create: DeploymentGroupAttributes = {
            route,
            description,
            type,
        } as DeploymentGroupAttributes;

        const instance = await dgModel.create(create);
        return instance.toInfo();
    }

    public async buildDeploymentGroupPath(...ids: number[]): Promise<string | undefined> {
        let result: string[] = [];

        for (const id of ids) {
            if (Number.isFinite(id)) {
                const group = await this.getDeploymentGroup(id);
                result.push(group.route);
            }
        }
        if (config.liveDeploymentGroupEnabled && result.includes(config.liveDeploymentGroupRoute)) {
            result = result.filter(value => value === config.liveDeploymentGroupRoute);
        }

        if (result.length > 0) {
            return result.join(DEPLOYMENT_ROUTE_SEPARATOR);
        } else {
            return undefined;
        }
    }

    public async getDeploymentGroups(): Promise<DeploymentGroupAttributes[]> {
        const groupsInstance = await dgModel.findAll();
        return groupsInstance.map(dg => dg.toInfo());
    }

    public async assignDeploymentGroupToGame(code: string, route: string, transaction?: Transaction): Promise<void> {
        const deploymentGroup = await this.getDeploymentGroupByRoute(route);

        const update: Game = {
            deploymentGroupId: deploymentGroup.id
        } as Game;

        const options: UpdateOptions = {
            where: { code: code },
            transaction
        };

        const [count] = await GameModel.update(update, options);
        if (count === 0) {
            throw new Errors.GameNotFoundError(code);
        }
    }

    public async assignDeploymentGroupToEntity(path: string, route: string): Promise<void> {

        const deploymentGroup = await this.getDeploymentGroupByRoute(route);
        const entity = await EntityCache.findOne({ path }, undefined, true, false);
        if (!entity.isBrand()) {
            throw new Errors.ValidationError("Deployment group can be assign only to brand or merchant");
        }

        const update: EntityAttributes = {
            deploymentGroupId: deploymentGroup.id
        } as EntityAttributes;

        const options: UpdateOptions = {
            where: { id: entity.id }
        };

        const [count] = await EntityModel.update(update, options);
        if (count === 0) {
            throw new Errors.EntityCouldNotBeFound();
        }
        EntityCache.reset();
    }

    public async unAssignDeploymentGroupFromGame(code: string): Promise<void> {
        const update: Game = {
            deploymentGroupId: null
        } as Game;

        const options: UpdateOptions = {
            where: { code: code }
        };

        const [count] = await GameModel.update(update, options);
        if (count === 0) {
            throw new Errors.GameNotFoundError(code);
        }
    }

    public async unAssignDeploymentGroupFromEntity(path: string): Promise<void> {

        const update: EntityAttributes = {
            deploymentGroupId: null
        } as EntityAttributes;

        const options: UpdateOptions = {
            where: { path: path }
        };

        const [count] = await EntityModel.update(update, options);
        if (count === 0) {
            throw new Errors.EntityCouldNotBeFound();
        }
        EntityCache.reset();
    }

    public async getAssignedGames(route: string): Promise<string[]> {
        const deploymentGroup = await this.getDeploymentGroupByRoute(route);

        const games = await GameModel.findAll({ where: { deploymentGroupId: deploymentGroup.id } });
        const result: string[] = [];

        for (const game of games) {
            result.push(game.get("code"));
        }

        return result;
    }

    public async getAssignedEntities(route: string): Promise<string[]> {
        const deploymentGroup = await this.getDeploymentGroupByRoute(route);

        const entities = await EntityModel.findAll({ where: { deploymentGroupId: deploymentGroup.id } });

        const result: string[] = [];

        for (const entity of entities) {
            result.push(entity.get("path"));
        }

        return result;
    }

    public invalidateCache() {
        this.groupsCacheById.clear();
        this.groupsCacheByRoute.clear();
    }

    private async loadDeploymentGroupById(id: number): Promise<DeploymentGroupAttributes> {
        const whereOptions: WhereOptions = { id: id };
        const options = { where: whereOptions };

        return this.loadDeploymentGroupByOptions(options);
    }

    private async loadDeploymentGroupByRoute(route: string): Promise<DeploymentGroupAttributes> {
        const whereOptions: WhereOptions = { route: route };
        const options = { where: whereOptions };

        return this.loadDeploymentGroupByOptions(options);
    }

    private async loadDeploymentGroupByOptions(options: FindOptions<any>): Promise<DeploymentGroupAttributes> {
        const group = await dgModel.findOne(options);

        if (!group) {
            throw new Errors.DeploymentGroupNotFound();
        }

        this.hitCache(group);

        return group.toInfo();
    }

    private hitCache(item: DeploymentGroupModel) {
        this.groupsCacheById.set(item.id, item.toInfo());
        this.groupsCacheByRoute.set(item.route, item.toInfo());
    }

}

export class BalancedGsApiProvider implements GameServerApiProvider {

    private static readonly PACKAGE_VERSION_ENDPOINT = "v1/critical-files/package-version";

    public async senPostByCtxId<T>(endpoint: string, req: any, entity: BrandEntity, contextId: string): Promise<T> {
        const game = await this.getGameByContextId(contextId, entity);
        const keys = contextId.split(":");
        return this.sendPost<T>(endpoint, req, entity, game, keys[3]);

    }

    // Ignore entity game status, when contextId is undefined, gameCode parameter is used
    public async sendPostForForceFinish<T>(endpoint: string,
                                           req: any,
                                           entity: BrandEntity,
                                           contextId: string,
                                           gameCode?: string): Promise<T> {
        // games:context:3:swftest_USER_306_sw_sod_EUR_HISTORY:sw_sod:web
        const keys = contextId?.split(":");
        let game;
        if (contextId) {
            const gameCodeParsed = keys[4];
            if (!gameCodeParsed) {
                throw new Errors.GameNotFoundError("not found gameCode");
            }
            game = await getGame(gameCodeParsed);
        } else if (gameCode) {
            game = await getGame(gameCode);
        }
        return this.sendPost<T>(endpoint, req, entity, game, keys?.[3]);
    }

    public async sendPostByGameCode<T>(endpoint: string, req: any, entity: BrandEntity, gameCode: string, playerCode: string): Promise<T> {
        const game = await this.getGameByCode(gameCode, entity);
        return this.sendPost<T>(endpoint, req, entity, game, playerCode);
    }

    public async sendPostByEntity<T>(endpoint: string, req: any, entity: BrandEntity, playerCode?: string): Promise<T> {
        const url = await buildDynamicGSUrl(entity, endpoint, playerCode);
        const route = await getDeploymentGroupService().buildDeploymentGroupPath(entity.deploymentGroupId);
        return this.post<T>(url, route, req);
    }

    private async sendPost<T>(endpoint: string, req: any, entity: BrandEntity, game: Game, playerCode: string): Promise<T> {
        const deploymentService = getDeploymentGroupService();

        let route: string;

        if (game) {
            route = await deploymentService.buildDeploymentGroupPath(entity.deploymentGroupId,
                game.deploymentGroupId);
        } else {
            route = await deploymentService.buildDeploymentGroupPath(entity.deploymentGroupId);
        }

        const url = await buildDynamicGSUrl(entity, endpoint, playerCode);

        return this.post<T>(url, route, req);
    }

    private async getGameByContextId(contextId: string, entity: BrandEntity): Promise<Game> {
        if (!contextId) {
            return undefined;
        }
        // games:context:3:swftest_USER_306_sw_sod_EUR_HISTORY:sw_sod:web
        const key = contextId.split(":");
        const gameCode = key[4];
        if (!gameCode) {
            throw new Errors.GameNotFoundError("not found gameCode");
        }

        return this.getGameByCode(gameCode, entity);
    }

    private async getGameByCode(gameCode: string, entity: BrandEntity): Promise<Game> {
        const entityGame = await findOneEntityGame(entity, gameCode);
        return entityGame.game;
    }

    private getItgProviderGameCode(gameInfo: EntityGame): string {
        const yamlVersion = gameInfo?.game?.clientFeatures?.yamlVersion;
        const providerGameCode = gameInfo.game.providerGameCode;
        return yamlVersion ? `${providerGameCode}_${yamlVersion}` : providerGameCode;
    }

    private getCodesAndProviderGameCodes(games: EntityGame[]): { code: string, providerGameCode: string }[] {
        const swGames = games.filter(gameInfo => !gameInfo.game.code.startsWith(ITG_GAMES_PREFIX));
        const swGamesProviderGameCodes = swGames.map(gameInfo => {
            return { code: gameInfo.game.code, providerGameCode: gameInfo.game.providerGameCode };
        });
        const itgGames = games.filter(gameInfo => gameInfo.game.code.startsWith(ITG_GAMES_PREFIX));
        const itgGamesProviderGameCodes = itgGames.map(gameInfo => {
            return { code: gameInfo.game.code, providerGameCode: this.getItgProviderGameCode(gameInfo) };
        });
        return [...swGamesProviderGameCodes, ...itgGamesProviderGameCodes];
    }

    private getProviderGameCodes(games: EntityGame[]): string[] {
        return this.getCodesAndProviderGameCodes(games).map(game => game.providerGameCode);
    }

    protected async requestGameCriticalFiles(entity: BaseEntity,
                                             gameDeploymentId: number,
                                             games: EntityGame[],
                                             regulation: MerchantRegulation,
                                             endpoint: string,
                                             customPort?: number,
                                             includeVersions?: boolean): Promise<GamesListWithHashes> {
        try {
            const deploymentService = getDeploymentGroupService();
            const route = await deploymentService.buildDeploymentGroupPath(entity.deploymentGroupId, gameDeploymentId);
            const providerGameCodes = this.getProviderGameCodes(games);
            const req: GetGameCriticalFilesInfoRequest = {
                regulation: regulation,
                // !NB, we search for critical files using providerGameCode, not gameCode!
                games: [...providerGameCodes, JPN_GAMES_MODULE]
            };
            const url = await this.buildDomainWithCustomPort(entity, endpoint, customPort);

            log.info({ url, req }, `Fetch game critical files, route:${route}; Games: ${games.length}`);
            const listWithHashes = await this.post<GamesListWithHashes>(url, route, req);
            log.info({ listWithHashes }, `Fetch game critical files response, route:${route}`);

            // mapping back from providerGameCode to gameCode
            const mappedListWithHashes = { games: [] };
            for (const gameWithHashes of listWithHashes.games) {
                const entityGames = games.filter(game => game.game.providerGameCode === gameWithHashes.code
                    || this.getItgProviderGameCode(game) === gameWithHashes.code
                );
                if (entityGames.length) {
                    for (const entityGame of entityGames) {
                        const newGameWithHashes = JSON.parse(JSON.stringify(gameWithHashes));
                        newGameWithHashes.code = entityGame.game.code;
                        mappedListWithHashes.games.push(newGameWithHashes);
                    }
                } else {
                    mappedListWithHashes.games.push(gameWithHashes);
                }
            }

            if (includeVersions) {
                const gameVersions = await this.requestGameVersions(entity, gameDeploymentId, regulation, games);
                for (const gameWithHashes of mappedListWithHashes.games) {
                    if (gameVersions[gameWithHashes.code]) {
                        gameWithHashes["version"] = gameVersions[gameWithHashes.code];
                    }
                }
            }

            const mustWinJackpotGameModule = listWithHashes.games.find(game => game.code === JPN_GAMES_MODULE);
            if (mustWinJackpotGameModule) {
                mappedListWithHashes.games.forEach((gameWithHashes) => {
                    const entityGame = games.find(game => game.game.code === gameWithHashes.code);
                    const entityGameSettings = entityGame && entityGame.settings;
                    if (entityGameSettings?.mustWinJackpotBundled) {
                        gameWithHashes.list = [
                            ...gameWithHashes.list,
                            ...mustWinJackpotGameModule.list.map(a => ({ ...a }))
                        ];
                    }
                });

                mappedListWithHashes.games = mappedListWithHashes.games.filter(game => game.code !== JPN_GAMES_MODULE);
            }

            return mappedListWithHashes;
        } catch (err) {
            log.error(err);
            return { games: [] };
        }
    }

    protected async requestGameVersions(entity: BaseEntity,
                                        gameDeploymentId: number,
                                        regulation: MerchantRegulation,
                                        games: EntityGame[]): Promise<GameCodesWithVersions> {

        const deploymentService = getDeploymentGroupService();
        const route = await deploymentService.buildDeploymentGroupPath(entity.deploymentGroupId, gameDeploymentId);
        const codesAndProviderGameCodes = this.getCodesAndProviderGameCodes(games);

        const url = await this.buildDomainWithCustomPort(entity,
            BalancedGsApiProvider.PACKAGE_VERSION_ENDPOINT,
            config.reportPOPCriticalFilesJob.apiPort);

        log.info({ url }, `Fetch modules versions, route:${route}; regulation: ${regulation}, games: ${games.length}`);

        const result = {};

        await Promise.all(codesAndProviderGameCodes.map(game => {
            return this.get<string>(url, route, { regulation, module: game.providerGameCode }).then(version => {
                result[game.code] = version;
            }).catch(error => {
                result[game.code] = `Not found. Reason ${error.message}`;
            });
        }));

        return result;
    }

    protected async requestHashGameCriticalFiles(entity: BaseEntity, gameDeploymentId: number,
                                                 regulation: MerchantRegulation, endpoint: string,
                                                 customPort?: number): Promise<any> {
        try {
            const deploymentService = getDeploymentGroupService();
            const route = await deploymentService.buildDeploymentGroupPath(entity.deploymentGroupId, gameDeploymentId);
            const req: HashCriticalFilesRequest = {
                regulation: regulation
            };
            const url = await this.buildDomainWithCustomPort(entity, endpoint, customPort);
            log.info({ url, req }, `Hash critical game files, route:${route}`);

            return this.post(url, route, req);
        } catch (err) {
            log.error(err);
            return;
        }
    }

    protected async buildDomainWithCustomPort(entity: BaseEntity,
                                              endpoint: string,
                                              customPort?: number): Promise<string> {
        const url = await buildDynamicGSUrl(entity, endpoint);

        if (!Number.isFinite(customPort)) {
            return url;
        } else {
            const urlHolder = new URL(url);
            urlHolder.port = "" + customPort;
            return urlHolder.toString();
        }
    }

    protected post<T>(url: string, route: string, req): Promise<T> {
        log.info({ url, req, route }, "Querying gameserver from management api service");

        return new Promise<T>((resolve, reject) => {
            request.post(url, {
                qs: { "sw_deployment": route },
                headers: {
                    "Content-Type": "application/json"
                },
                body: req,
                json: true
            }, this.processResponse(resolve, reject));
        });
    }

    protected get<T>(url: string, route: string, req): Promise<T> {
        log.info({ url, route }, "Querying gameserver from management api service");

        return new Promise<T>((resolve, reject) => {
            request.get(url, {
                qs: { "sw_deployment": route, ...req },
                headers: {
                    "Content-Type": "application/json"
                },
                json: true
            }, this.processResponse(resolve, reject));
        });
    }

    protected processResponse(resolve, reject): (error: any, response: IncomingMessage, body: any) => Promise<any> {
        return function(error: Error, response: IncomingMessage, body: any): Promise<any> {
            if (error) {
                log.error(error, "Failed to query game server");
                return reject(new Errors.ErrorQueryingGameServer());
            } else if (response.statusCode < 200 || response.statusCode >= 300) {
                log.error({ body, statusCode: response.statusCode }, "Failed to process request");

                if (body?.code === 520) {
                    return reject(new Errors.ForbidToRecoverGameWithJPError());
                } else if (body?.code === 533 || body?.code === 10) {
                    return reject(new Errors.GameHistoryDetailsNotFound());
                } else {
                    return reject(
                        new Errors.GameServerRequestError(body && isSWGameServerError(body) ? body : undefined));
                }

            } else {
                return resolve(body);
            }
        };
    }
}

const deploymentGroupHelper = lazy<DeploymentGroupService>(() => new CacheableDeploymentGroupService());

export function getDeploymentGroupService(): DeploymentGroupService {
    return deploymentGroupHelper.get();
}

const gsApiProvider = lazy<GameServerApiProvider>(() => new BalancedGsApiProvider());

export function getGameServerApiProvider(): GameServerApiProvider {
    return gsApiProvider.get();
}
