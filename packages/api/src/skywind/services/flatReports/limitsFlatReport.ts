import { AbstractFlatReport, FlatReportOptions } from "./flatReport";
import { BaseEntity, ENTITY_TYPE as EntityType } from "../../entities/entity";
import { FLAT_REPORT_TYPE } from "../../entities/flatReport";
import { getEntitySettings } from "../settings";
import { getGameGroupService } from "../gamegroup";
import { findPlayerLimits } from "../limits";
import { Cur<PERSON>cyCode, GameGroupInfo, LimitType } from "../../entities/gamegroup";
import { LimitsForCurrencyNotFound } from "../../errors";
import { BrandEntity } from "../../entities/brand";

import { logging } from "@skywind-group/sw-utils";
import { getNewLimitsFacade } from "../gameLimits/limitsFacade";
import { EntityGame } from "../../entities/game";
import { EntitySettings } from "../../entities/settings";
import { getEntityGames } from "../entityGameService";

const log = logging.logger("limits-flat-report");

abstract class LimitsFlatReport extends AbstractFlatReport {
    protected constructor(type: FLAT_REPORT_TYPE, options: FlatReportOptions) {
        super(type, [EntityType.MERCHANT, EntityType.BRAND], options);
    }

    protected async buildReport(entity: BaseEntity): Promise<any> {
        const brandEntity = entity as BrandEntity;
        const settings = await getEntitySettings(entity.path);
        const games = await getEntityGames(entity);
        const gameGroups = await getGameGroupService().findAll(entity, settings);
        const report = {};
        for (const game of games) {
            const gameCode = game.toInfo().code;
            const currencies = entity.getCurrencies();
            report[gameCode] = {};
            for (const gameGroup of gameGroups) {
                report[gameCode][gameGroup.name] = {};
                for (const currency of currencies) {
                    try {
                        const limitsReport = await this.buildLimits(brandEntity, game, gameGroup, currency, settings);
                        report[gameCode][gameGroup.name][currency] = limitsReport;
                    } catch (err) {
                        if (err instanceof LimitsForCurrencyNotFound) {
                            log.warn(`Limits for game ${gameCode} and currency ${currency} are not defined`);
                        } else {
                            throw err;
                        }
                    }
                }
            }
        }
        return report;
    }

    protected abstract buildLimits(entity: BrandEntity,
                                   game: EntityGame,
                                   gameGroup: GameGroupInfo,
                                   currency: CurrencyCode,
                                   settings: EntitySettings);
}

export class OldLimitsFlatReport extends LimitsFlatReport {

    constructor(options: FlatReportOptions) {
        super(FLAT_REPORT_TYPE.OLS, options);
    }

    protected async buildLimits(entity: BrandEntity,
                                game: EntityGame,
                                gameGroup: GameGroupInfo,
                                currency: CurrencyCode,
                                settings: EntitySettings): Promise<any> {

        const [limits, limitsCustomizations] = await findPlayerLimits(
            entity as BrandEntity,
            game,
            currency,
            gameGroup.name,
            settings
        );

        return {
            limits,
            limitsCustomizations: limitsCustomizations.map(v => LimitType[v])
        };
    }
}

export class NewLimitsFlatReport extends LimitsFlatReport {

    constructor(options: FlatReportOptions) {
        super(FLAT_REPORT_TYPE.NLS, options);
    }

    public async buildLimits(entity: BrandEntity,
                             game: EntityGame,
                             gameGroup: GameGroupInfo,
                             currency: string,
                             settings: EntitySettings): Promise<any> {

        const segmentId = undefined; // TODO implement me
        const limits = entity.isMerchant ?
                       await getNewLimitsFacade(entity).buildGameLaunch({
                           game: game.game,
                           currency,
                           gameGroupId: gameGroup.id,
                           segmentId,
                           isMPSupported: settings.isMarketplaceSupported
                       }) :
                       await getNewLimitsFacade(entity).buildGameLaunch({
                           game: game.game,
                           currency,
                           gameGroupId: gameGroup.id
                       });
        return {
            limits
        };
    }
}
