import { BaseEntity, <PERSON>ti<PERSON><PERSON>ith<PERSON>hild, MIGRATION_STATUS } from "../entities/entity";
import * as Errors from "../errors";
import { getDynamicDomainPoolService } from "./dynamicDomainPool";
import { DynamicDomainPoolAttributes, ExtendedDynamicDomain, } from "../entities/domainPool";
import * as HashRing from "hashring";
import config from "../config";
import { sequelize as db } from "../storage/db";
import { ApplicationLock, ApplicationLockId } from "../utils/applicationLock";
import { Models } from "../models/models";
import EntityCache from "../cache/entity";
import { Transaction } from "sequelize";
import MigrationService from "./migrationService";
import { DynamicDomain } from "../entities/domain";
import { getDomainService } from "./domain";

function pickRandom<T>(items: T[]): T {
    const randomIndex = Math.floor(Math.random() * items.length);
    return items[randomIndex];
}

const hashRings = new Map<string, HashRing>();

export class EntityDynamicDomainPoolService {
    constructor(
        private readonly entity: BaseEntity,
        private readonly domainPoolService = getDynamicDomainPoolService(),
        private readonly dynamicDomainService = getDomainService()
    ) {
    }

    public async getPool(inherited: boolean = false): Promise<DynamicDomainPoolAttributes> {
        if (inherited) {
            if (!this.entity.inheritedDynamicDomainPoolId) {
                throw new Errors.EntityDomainPoolNotDefinedError();
            }
            const pool = await this.domainPoolService.findById(this.entity.inheritedDynamicDomainPoolId);
            if (!pool) {
                throw new Errors.EntityDomainPoolNotDefinedError();
            }
            return pool;
        } else {
            if (!this.entity.dynamicDomainPoolId) {
                throw new Errors.EntityDomainPoolNotDefinedError();
            }
            return this.domainPoolService.findById(this.entity.dynamicDomainPoolId);
        }
    }

    public async addPool(poolId: number): Promise<DynamicDomainPoolAttributes> {
        const dynamicDomainPool = await this.domainPoolService.findById(poolId);
        if (!dynamicDomainPool) {
            throw new Errors.DomainPoolNotFoundError();
        }
        return db.transaction(async (transaction) => {
            await this.doMigrationForAddedPool(this.entity, dynamicDomainPool, transaction);
            this.entity.dynamicDomainPoolId = poolId;
            await this.entity.save(transaction);
            EntityCache.reset();
            return dynamicDomainPool;
        });
    }

    private async doMigrationForAddedPool(
        entity: BaseEntity,
        dynamicDomainPool: DynamicDomainPoolAttributes,
        transaction: Transaction
    ): Promise<void> {
        if (entity.isBrand()) {
            const entityDynamicDomainPoolId = entity.inheritedDynamicDomainPoolId;
            const entityDynamicDomainId = entity.inheritedDynamicDomainId;
            if (entityDynamicDomainPoolId) {
                const existingDomainPool = await this.domainPoolService.findById(entityDynamicDomainPoolId);
                const existingEnvironments = existingDomainPool.domains.map(d => d.environment);
                const newEnvironments = dynamicDomainPool.domains.map(d => d.environment);
                const hasNewEnvironments = newEnvironments.some(env => !existingEnvironments.includes(env));
                const hasFewerEnvironments = existingEnvironments.some(env => !newEnvironments.includes(env));
                if (hasNewEnvironments || hasFewerEnvironments) {
                    await this.triggerMigration(this.entity, existingDomainPool.domains, transaction);
                }
            } else if (entityDynamicDomainId && dynamicDomainPool.domains.some(d => d.id !== entityDynamicDomainId)) {
                const existingDynamicDomain = await this.dynamicDomainService.findOne(entityDynamicDomainId) as DynamicDomain;
                if (!existingDynamicDomain) {
                    throw new Errors.DomainNotFoundError(entityDynamicDomainId);
                }
                if (dynamicDomainPool.domains.some(d => d.environment !== existingDynamicDomain.environment)) {
                    await this.triggerMigration(this.entity, [existingDynamicDomain], transaction);
                }
            }
        } else {
            const entityWithChild = entity as EntityWithChild;
            for (const child of entityWithChild.child) {
                await this.doMigrationForAddedPool(child, dynamicDomainPool, transaction);
            }
        }
    }

    public async removePool(): Promise<void> {
        if (!this.entity.inheritedDynamicDomainPoolId) {
            return;
        }
        await db.transaction(async (transaction) => {
            const [dynamicDomainPool] = await this.domainPoolService.findAll({
                where: {
                    id: this.entity.inheritedDynamicDomainPoolId
                },
                transaction
            });
            if (!dynamicDomainPool) {
                throw new Errors.DomainPoolNotFoundError();
            }
            await this.doMigrationForRemovedPool(this.entity, dynamicDomainPool, transaction);
            this.entity.dynamicDomainPoolId = null;
            await this.entity.save(transaction);
            EntityCache.reset();
        });
    }

    private async doMigrationForRemovedPool(
        entity: BaseEntity,
        dynamicDomainPool: DynamicDomainPoolAttributes,
        transaction: Transaction
    ): Promise<void> {
        if (entity.isBrand()) {
            const dynamicDomain = await this.dynamicDomainService.findOne(this.entity.inheritedDynamicDomainId) as DynamicDomain;
            if (!dynamicDomain) {
                return;
            }
            const newEnvironments = dynamicDomainPool.domains.map(d => d.environment);
            if (newEnvironments.some(env => env !== dynamicDomain.environment)) {
                await this.triggerMigration(this.entity, dynamicDomainPool.domains, transaction);
            }
        } else {
            const entityWithChild = entity as EntityWithChild;
            for (const child of entityWithChild.child) {
                await this.doMigrationForRemovedPool(child, dynamicDomainPool, transaction);
            }
        }
    }

    public async pickDynamicDomain(playerCode: string): Promise<ExtendedDynamicDomain | undefined> {
        if (!playerCode || !this.entity.inheritedDynamicDomainPoolId) {
            return undefined;
        }
        const pool = await this.domainPoolService.findById(this.entity.inheritedDynamicDomainPoolId);
        const domains = pool?.domains;
        if (!domains?.length) {
            return undefined;
        }
        const environment = this.getDomainEnvironmentFromHashRing(
            domains.map(domain => domain.environment),
            playerCode
        );
        const filteredDomains = domains.filter(d => d.environment === environment && d.isActive);
        if (!domains.length) {
            return undefined;
        }
        return pickRandom(filteredDomains);
    }

    private getDomainEnvironmentFromHashRing(environments: string[], playerCode: string): string {
        const key = `${this.entity.key}-${environments.join("-")}`;
        let hashRing: HashRing;
        if (hashRings.has(key)) {
            hashRing = hashRings.get(key);
        } else {
            hashRing = new HashRing(
                environments.sort(),
                config.dynamicDomainPoolHashRing.algorithm,
                {
                    "vnode count": config.dynamicDomainPoolHashRing.vNodeCount,
                    replicas: config.dynamicDomainPoolHashRing.replicas,
                    "max cache size": config.dynamicDomainPoolHashRing.maxCacheSize
                }
            );
            hashRings.set(key, hashRing);
        }
        return hashRing.get(`${this.entity.id.toString()}-${playerCode}`);
    }

    private async triggerMigration(
        entity: BaseEntity,
        domains: DynamicDomain[],
        transaction: Transaction
    ): Promise<void> {
        const updatedEntities: number[] = [];
        await ApplicationLock.lock(transaction, ApplicationLockId.UPDATE_DYNAMIC_DOMAIN_POOL);
        if (entity.isBrand()) {
            await Models.EntityModel.update(
                {
                    migrationStatus: MIGRATION_STATUS.STARTED,
                } as any,
                { where: { id: entity.id }, transaction }
            );
            updatedEntities.push(entity.id);
        } else {
            await this.markChildrenForProgressingMigration(entity, updatedEntities, transaction);
        }

        EntityCache.reset();
        for (const id of updatedEntities) {
            await MigrationService.startMigration(id, domains, transaction)
        }
    }

    private async markChildrenForProgressingMigration(
        entity: BaseEntity,
        updatedEntities: number[],
        transaction: Transaction
    ): Promise<void> {
        const entityWithChild = entity as EntityWithChild;
        if (entityWithChild.child) {
            for (const child of entityWithChild.child) {
                if (child.migrationStatus === MIGRATION_STATUS.STARTED || child.migrationStatus === MIGRATION_STATUS.PROCESSING) {
                    return Promise.reject(new Errors.MigrationIsInProgressError());
                }
                if (!child.dynamicDomainPoolId) {
                    if (child.isBrand()) {
                        await Models.EntityModel.update({
                                migrationStatus: MIGRATION_STATUS.STARTED,
                            } as any,
                            { where: { id: child.id }, transaction });
                        updatedEntities.push(child.id);
                    } else {
                        await this.markChildrenForProgressingMigration(
                            child, updatedEntities, transaction);
                    }
                }
            }
        }
    }
}
