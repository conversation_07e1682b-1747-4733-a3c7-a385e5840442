import { BonusCoinRewardInfo, FreebetRewardInfo } from "../../entities/promotion";
import PlayerGameSessionService from "../player/playerGameSessionService";
import { BrandEntity } from "../../entities/brand";
import { PlayerPromotionService } from "./playerPromotionService";
import { measures } from "@skywind-group/sw-utils";
import { PlayerGameFreebet, PROMO_TYPE } from "@skywind-group/sw-management-promo-wallet";
import { PlayerSessionInfo } from "@skywind-group/sw-management-playersession";
import measure = measures.measure;
import { PlayerGamePromo } from "@skywind-group/sw-management-gameprovider";
import { PromotionImpl } from "./promotion";

export class PlayerSessionPromotion {

    @measure({ name: "PlayerSessionPromotion.getGamePromos", isAsync: true, debugOnly: true })
    public async getGamePromos(brand: BrandEntity, playerCode: string,
                               currency: string, gameCode: string,
                               sharedPromoEnabled = false,
                               playerBonusesEnabled?: boolean): Promise<PlayerGamePromo> {
        const gameFreeBets = await PlayerPromotionService.getGamePromotion(brand, playerCode, currency, gameCode,
             PROMO_TYPE.FREEBET, sharedPromoEnabled, playerBonusesEnabled);

        const gameBonusCoin = await PlayerPromotionService.getGamePromotion(brand, playerCode, currency, gameCode,
            PROMO_TYPE.BONUS_COIN);

        return {
            freeBet: gameFreeBets,
            bonusCoin: gameBonusCoin
        };
    }

    @measure({ name: "PlayerSessionPromotion.notifyPromoAdded", isAsync: true, debugOnly: true })
    public async notifyPromoAdded(brandId: number,
                                  playerCode: string,
                                  playerCurrency: string,
                                  promo: PromotionImpl): Promise<void> {
        const session = await PlayerGameSessionService.findAll(brandId, playerCode);
        if (!session) {
            return;
        }

        let updated = false;
        switch (promo.getType()) {
            case PROMO_TYPE.BONUS_COIN:
                updated = updated || this.updateSessionBonusCoin(session, playerCurrency, promo);
                break;

            case PROMO_TYPE.FREEBET:
                updated = updated || this.updateSessionFreeBets(session, playerCurrency, promo);
                break;

            default:
            // do nothing
        }

        if (updated) {
            await PlayerGameSessionService.updateAll(brandId, playerCode, session);
        }
    }

    private updateSessionBonusCoin(session: PlayerSessionInfo, playerCurrency: string, promo: PromotionImpl): boolean {
        let updated = false;
        for (const reward of promo.getRewards()) {
            const info: BonusCoinRewardInfo = reward.toInfo() as BonusCoinRewardInfo;
            for (const game of info.games) {
                if (!session[game]) {
                    continue;
                }
                if (!session[game].promo) {
                    session[game].promo = {};
                }
                if (info.exchangeRates[playerCurrency]) {
                    session[game].promo.bonusCoin = {
                        promoId: promo.getId(),
                        rewardId: reward.getId(),
                        exchangeRate: info.exchangeRates[playerCurrency]
                    };
                    updated = true;
                }
            }
        }
        return updated;
    }

    private updateSessionFreeBets(session: PlayerSessionInfo, playerCurrency: string, promo: PromotionImpl): boolean {
        let updated = false;
        for (const reward of promo.getRewards()) {
            const info: FreebetRewardInfo = reward.toInfo() as FreebetRewardInfo;
            for (const game of info.games) {
                if (!session[game.gameCode]) {
                    continue;
                }
                const coinConfig = game.coins.find(config => config[playerCurrency] !== undefined);
                if (coinConfig) {
                    if (!session[game.gameCode].promo) {
                        session[game.gameCode].promo = {};
                    }
                    const newValue = session[game.gameCode].promo.freeBet || [];
                    const hasPromo = newValue.find(
                        item => item.promoId === promo.getId() && item.rewardId === reward.getId());
                    if (!hasPromo) {
                        const gameFreebet: PlayerGameFreebet = {
                            promoId: promo.getId(),
                            rewardId: reward.getId(),
                            coin: coinConfig[playerCurrency].coin
                        };

                        if (promo.isExternal()) {
                            gameFreebet.externalId = promo.getExternalId();
                        }

                        newValue.push(gameFreebet);
                        session[game.gameCode].promo.freeBet = newValue;
                        updated = true;
                    }
                }
            }
        }
        return updated;
    }
}

export default new PlayerSessionPromotion();
