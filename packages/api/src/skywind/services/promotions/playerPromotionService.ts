import { measures } from "@skywind-group/sw-utils";
import measureProvider = measures.measureProvider;

import { PlayerInfo } from "../../entities/player";
import {
    DEFAULT_SORT_KEY as PLAYER_DEFAULT_SORT_KEY, getBrandPlayerService,
    PlayerImpl,
    sortableKeys as playerSortableKeys
} from "../brandPlayer";
import { getBrandPlayerValidator } from "../brandPlayerValidator";
import {
    PromotionToPlayer,
    PromotionToPlayerImpl,
    PromotionToPlayerStatus
} from "../../models/promotionPlayer";
import { PagingHelper } from "../../utils/paginghelper";
import * as PromoService from "./promotion";
import * as Errors from "../../errors";
import * as FilterService from "../filter";
import { parseFilter } from "../filter";
import { literal, Op, WhereOptions } from "sequelize";
import { BrandEntity } from "../../entities/brand";
import { PlayerPromotionDb, PlayerPromotionInfo, PromotionAddResult, PromotionUpdateResult } from "./playerPromotionDb";
import { Models } from "../../models/models";
import * as schedule from "node-schedule";
import logger from "../../utils/logger";
import * as PlayerRewardServices from "./playerRewardServices";
import { PromotionRewardService } from "./promotionRewardService";
import EntityCache from "../../cache/entity";
import { PROMO_TYPE } from "@skywind-group/sw-management-promo-wallet";
import { PromotionPlayersUpdate } from "./promotionPlayersUpdate";
import { destructureEGPPromoId, getEGPConfiguration, getEGPPromoGateway, PROMO_LOCATION } from "./egpPromoGateway";
import { BaseEntity } from "../../entities/entity";
import { getEGPCodes, PromotionImpl } from "./promotion";

const PlayerModel = Models.PlayerModel;
const GameGroupModel = Models.GameGroupModel;
const AgentModel = Models.AgentModel;

const log = logger("player-promotion");

const queryPlayerParamsKeys = [
    "limit",
    "code",
    "status",
    "firstName",
    "lastName",
    "email",
    "country",
    "language",
    "currency",
    "gamegroupId",
    "lastLogin",
    "createdAt",
    "updatedAt",
    "isTest"
];

export class PlayerPromotionServiceImpl {

    public async addPromotionToPlayer(brand: BrandEntity,
                                      promoId: number | string,
                                      playerCode: string,
                                      data?: any): Promise<PlayerPromotionInfo[]> {
        await this.addPromotionToPlayers(brand, promoId, [playerCode], data);
        // Check if the promoId is a string - it means it is an external promotion
        // We should not attempt to search the promotion by id in our database
        if (typeof promoId === "string") {
            return this.getPlayerPromotions(brand, playerCode);
        }
        const promo: PromotionImpl = await PromoService.findOne({ id: promoId, brandId: brand.id });
        return this.getPlayerPromotions(brand, playerCode, promo.getType());
    }

    public async addPromotionToPlayersByFilter(brand: BrandEntity, promoId: number, filters: any,
                                               data?: any): Promise<PromotionAddResult> {
        let playerCodes: string[] = [];
        const playerFilter = parseFilter(filters, queryPlayerParamsKeys);
        if (playerFilter["code"] && Object.keys(playerFilter).length === 1) {
            playerCodes = playerFilter["code"][Op.in];
        } else if (!brand.isMerchant) {
            const players = await getBrandPlayerService().searchPromoPlayers(brand, playerFilter);
            playerCodes = players.map((player) => player.code);
        }
        return this.addPromotionToPlayers(brand, promoId, playerCodes, data);
    }

    public async addPromotionToPlayers(brand: BrandEntity, promoId: number | string, playerCodes: string[],
                                       data?: any): Promise<PromotionAddResult> {
        await getBrandPlayerValidator().validatePlayerCodes(brand, playerCodes);
        let addedPlayerCodes = playerCodes;
        const destructuredEGPPromoId = destructureEGPPromoId(promoId);
        if (destructuredEGPPromoId) {
            const { egpPromoId, gameProviderCode } = destructuredEGPPromoId;
            const { url } = getEGPConfiguration(gameProviderCode);
            const egpPromoGateway = getEGPPromoGateway(url, gameProviderCode);
            const egpPromoInfo = await egpPromoGateway.addPlayersToPromo(egpPromoId, playerCodes, data);
            addedPlayerCodes = egpPromoInfo.customerIds;
        } else {
            const promo = await PromoService.findOne({ id: promoId, brandId: brand.id });
            const rewardService = PlayerRewardServices.get(brand, promo.getType());
            await rewardService.addPlayers(promo, playerCodes, data, undefined, brand);
        }

        const addResult: PromotionAddResult = {};
        for (const code of addedPlayerCodes) {
            addResult[code] = "OK";
        }

        return addResult;
    }

    public async getPromotionPlayers(brand: BrandEntity,
                                     promoId: number,
                                     query?: WhereOptions<any>,
                                     limitQuery?: WhereOptions<any>): Promise<PlayerPromotionInfo[]> {
        const destructuredEGPPromoId = destructureEGPPromoId(promoId);
        if (destructuredEGPPromoId) {
            const { egpPromoId, gameProviderCode } = destructuredEGPPromoId;
            const { url } = getEGPConfiguration(gameProviderCode);
            const egpPromoGateway = getEGPPromoGateway(url, gameProviderCode);
            return egpPromoGateway.getPromoPlayers(egpPromoId);
        }

        const promo: PromotionImpl = await PromoService.findOne({ id: promoId, brandId: brand.id });

        return PlayerRewardServices.get(brand, promo.getType()).getPlayers(promo, query, limitQuery);
    }

    public async removePlayerFromPromotion(brand: BrandEntity, promoId: number, playerCode: string,
                                           force?: boolean): Promise<void> {
        const destructuredEGPPromoId = destructureEGPPromoId(promoId);
        if (destructuredEGPPromoId) {
            const { egpPromoId, gameProviderCode } = destructuredEGPPromoId;
            const { url } = getEGPConfiguration(gameProviderCode);
            return getEGPPromoGateway(url, gameProviderCode).removePlayersFromPromo(egpPromoId, playerCode);
        }

        const promo: PromotionImpl = await PromoService.findOne({ id: promoId, brandId: brand.id });
        if (promo.hasFinished() && !force) {
            return Promise.reject(new Errors.PromoIsNotInValidState());
        }

        const playerPromotion: PromotionToPlayerImpl = await PlayerPromotionDb.getPlayerPromotion(playerCode, promoId);

        if (!playerPromotion) {
            return Promise.reject(new Errors.PlayerHasNoSuchPromoError());
        }

        if (playerPromotion.status === PromotionToPlayerStatus.PENDING) {
            await PlayerPromotionDb.removePlayerPromotion(playerCode, promoId);
        } else {
            if (force) {
                await PlayerRewardServices.get(brand, promo.getType()).revokePlayer(promo, playerPromotion);
            } else {
                await PlayerRewardServices.get(brand, promo.getType()).removePlayer(promo, playerPromotion);
            }
        }
    }

    public async removePlayersFromPromotion(brand: BrandEntity, promoId: number, playerCodes: string[]): Promise<void> {
        const promo: PromotionImpl = await PromoService.findOne({ id: promoId, brandId: brand.id });
        if (promo.hasFinished()) {
            return Promise.reject(new Errors.PromoIsNotInValidState());
        }
        const playerPromotions: PromotionToPlayerImpl[] =
            await PlayerPromotionDb.getPlayersPromotions(playerCodes, promoId);

        if (!playerPromotions || !playerPromotions.length) {
            return Promise.reject(new Errors.PlayerHasNoSuchPromoError());
        }

        if (playerPromotions.find(p => p.rewardedAt)) {
            return Promise.reject(new Errors.ForbiddenToRemovePromo());
        }

        const pendingPlayers = playerPromotions.filter(p => p.status === PromotionToPlayerStatus.PENDING)
            .map(p => p.playerCode);

        const otherPlayers = playerPromotions.filter(p => p.status !== PromotionToPlayerStatus.PENDING);

        if (pendingPlayers && pendingPlayers.length) {
            await PlayerPromotionDb.removePromotionPlayers(pendingPlayers, promoId);
        }

        if (otherPlayers && otherPlayers.length) {
            const update = new PromotionPlayersUpdate(promo, otherPlayers);
            update.updateAllPlayerPromos({ status: PromotionToPlayerStatus.REVOKED });
            await update.commit();
        }
    }

    /**
     * Searches for players, excludes players that already have promo applied.
     * Must be changed later to get players from BI
     */
    public async getPromoEligiblePlayers(brand: BrandEntity, promoId: number,
                                         query?: WhereOptions<any>): Promise<PlayerInfo[]> {

        const promo: PromotionImpl = await PromoService.findOne({ id: promoId });
        const isBnsPromo = promo.getType() === PROMO_TYPE.BONUS_COIN;

        await PromoService.checkPromoAllowed(brand, true, isBnsPromo);
        // TODO: get promo conditions and get players from BI that satisfy promo conditions, add them to query

        const playersWithPromo: PromotionToPlayer[] = await PlayerPromotionDb.getPromotionPlayers(promoId);

        let gameGroupQuery: WhereOptions<any>;
        if (query["gameGroup"]) {
            gameGroupQuery = {
                name: query["gameGroup"],
            };
            delete query["gameGroup"];
        }
        query["brandId"] = brand.id;
        if (playersWithPromo && playersWithPromo.length) {
            query["id"] = { [Op.notIn]: playersWithPromo.map((p) => p.playerId) };
        }

        const sortBy = FilterService.getSortKey(query, playerSortableKeys, PLAYER_DEFAULT_SORT_KEY);
        const sortOrder = FilterService.valueFromQuery(query, "sortOrder") || "ASC";

        return PagingHelper.findAsyncAndCountAll(PlayerModel, {
            where: query,
            offset: FilterService.valueFromQuery(query, "offset"),
            limit: FilterService.valueFromQuery(query, "limit"),
            order: literal(`"${sortBy}" ${sortOrder}`),
            include: [
                {
                    model: GameGroupModel,
                    where: gameGroupQuery,
                },
                {
                    model: AgentModel,
                },
            ],
        }, player => {
            const instance = new PlayerImpl(player, brand);
            return instance.toInfo();
        });
    }

    public countPromoParticipatedPlayers(promotionId: number): Promise<number> {
        return PlayerPromotionDb.countPromotionPlayers(promotionId);
    }

    public async getPlayerPromotions(brand: BrandEntity,
                                     playerCode: string,
                                     type?: string,
                                     skipEGPPromotions?: boolean): Promise<PlayerPromotionInfo[]> {
        const playerPromotions = await PlayerPromotionDb.getPlayerPromotions(brand.id, playerCode, type);

        if (!playerPromotions.length) {
            if (skipEGPPromotions) {
                return [];
            }
            return this.getEGPPlayerPromotions(brand, playerCode);
        }

        const info: PlayerPromotionInfo[] = [];

        const rewardServices: Map<string, PromotionRewardService> = new Map<string, PromotionRewardService>();
        for (const playerPromo of playerPromotions) {
            if (!rewardServices.has(playerPromo.promoType)) {
                rewardServices.set(playerPromo.promoType, PlayerRewardServices.get(brand, playerPromo.promoType));
            }
            info.push(await rewardServices.get(playerPromo.promoType).getPromotionInfo(playerPromo));
        }
        if (!skipEGPPromotions) {
            const egpPlayerPromotions = await this.getEGPPlayerPromotions(brand, playerCode);
            return [...info, ...egpPlayerPromotions];
        }
        return info;
    }

    private async getEGPPlayerPromotions(entity: BaseEntity, playerCode: string): Promise<PlayerPromotionInfo[]> {
        const egpCodes = await getEGPCodes(entity);
        if (!egpCodes || !egpCodes.length) {
            return [];
        }
        const egpPlayerPromotions: PlayerPromotionInfo[] = [];
        for (const egpCode of egpCodes) {
            const egpConfiguration = getEGPConfiguration(egpCode, true);
            if (egpConfiguration?.promoLocation === PROMO_LOCATION.EGP) {
                const egpPromoGateway = getEGPPromoGateway(egpConfiguration.url, egpCode);
                const foundEGPPlayerPromotions = await egpPromoGateway.getPlayerPromotions(entity, playerCode);
                egpPlayerPromotions.push(...foundEGPPlayerPromotions);
            }
        }
        return egpPlayerPromotions;
    }

    public async getPlayerPromotion(brand: BrandEntity, playerCode: string,
                                    promoId: number): Promise<PlayerPromotionInfo> {
        const destructuredEGPPromoId = destructureEGPPromoId(promoId);
        if (destructuredEGPPromoId) {
            const { egpPromoId, gameProviderCode } = destructuredEGPPromoId;
            const { url } = getEGPConfiguration(gameProviderCode);
            return getEGPPromoGateway(url, gameProviderCode).getPlayerPromotion(egpPromoId, playerCode);
        }
        const promo: PromotionImpl = await PromoService.findOne({ id: promoId, brandId: brand.id });
        const playerPromo: PromotionToPlayer = await PlayerPromotionDb.getPlayerPromotion(playerCode, promoId);

        if (!playerPromo) {
            return Promise.reject(new Errors.PlayerHasNoSuchPromoError());
        }

        return PlayerRewardServices.get(brand, promo.getType()).getPromotionInfo(playerPromo);
    }

    public async getFreeBetLeft(brand: BrandEntity, playerCode: string,
                                promoId: number) {
        const promo: PromotionImpl = await PromoService.findOne({ id: promoId, brandId: brand.id });
        const playerPromo: PromotionToPlayer = await PlayerPromotionDb.getPlayerPromotion(playerCode, promoId);

        if (!playerPromo) {
            return Promise.reject(new Errors.PlayerHasNoSuchPromoError());
        }
        return PlayerRewardServices.get(brand, promo.getType()).getFreeBeetLeft(playerPromo, brand);
    }

    public async updatePlayerPromotion(brand: BrandEntity, playerCode: string,
                                       promoId: number, data: any): Promise<PlayerPromotionInfo> {
        const promo: PromotionImpl = await PromoService.findOne({ id: promoId, brandId: brand.id });
        if (!promo.isRunning() || promo.hasFinished()) {
            return Promise.reject(new Errors.PromoIsNotInValidState());
        }

        const playerPromo: PromotionToPlayerImpl = await PlayerPromotionDb.getPlayerPromotion(playerCode, promoId);

        if (!playerPromo) {
            return Promise.reject(new Errors.PlayerHasNoSuchPromoError());
        }

        const rewardService = PlayerRewardServices.get(brand, promo.getType());
        await rewardService.updatePlayer(promo, playerPromo, data);

        return rewardService.getPromotionInfo(playerPromo);
    }

    public async updatePromotionPlayers(brand: BrandEntity,
                                        playerCodes: string[],
                                        promo: PromotionImpl,
                                        data: any): Promise<void> {
        if (!promo.isRunning() || promo.hasFinished()) {
            return Promise.reject(new Errors.PromoIsNotInValidState());
        }
        const promoPlayers: PromotionToPlayerImpl[] = await PlayerPromotionDb.getPromotionPlayers(promo.getId(),
            { playerCode: { [Op.in]: playerCodes } });

        const rewardService = PlayerRewardServices.get(brand, promo.getType());
        await rewardService.updatePlayersList(promo, promoPlayers, data);
    }

    public async getGamePromotion(
        brand: BrandEntity,
        playerCode: string,
        currency: string,
        gameCode: string,
        promoType: string,
        sharedPromoEnabled?: boolean,
        playerBonusesEnabled?: boolean
    ): Promise<any> {
        try {
            const rewardService = PlayerRewardServices.get(brand, promoType, sharedPromoEnabled);
            return await rewardService.getGamePromotion(
                playerCode,
                currency,
                gameCode,
                promoType,
                brand,
                playerBonusesEnabled
            );
        } catch (err) {
            log.error(err,
                "Fails to get game promotion: brand=%s, playerCode=%s, playerCurrency=%s, gameCode=%s, type=%s",
                brand.id, playerCode, currency, gameCode, promoType);
        }
    }

    public async startPromotion(brand: BrandEntity, promoId: number): Promise<PromotionImpl> {
        const result = await this.startPromotions(brand, [promoId]);
        const promo = result[0];
        if (!promo) {
            return Promise.reject(new Errors.PromoIsNotInValidState());
        }
        return promo;
    }

    public async startPromotions(brand: BrandEntity, promoIds: number[], userId?: number): Promise<PromotionImpl[]> {
        return PromoService.setPromosStarted({
            brandId: brand.id,
            id: { [Op.in]: promoIds },
            active: true,
            archived: false,
            startDate: { [Op.lte]: new Date() },
            endDate: { [Op.gte]: new Date() }
        }, async (promo) => {
            log.info("Start pending promotion", promo.toShortInfo());
            try {
                const result = await this.doStartPromotion(brand, promo);
                log.info("Pending promotion started", promo.getId(), result);
            } catch (err) {
                log.error(err, "Failed to start pending promotion", promo.toShortInfo());
            }
        }, userId);
    }

    public async startPendingPromotions(): Promise<PromotionImpl[]> {
        return measureProvider.runInTransaction("Pending promotions", async () => {
            return PromoService.setPromosStarted({
                active: true,
                archived: false,
                startDate: { [Op.lte]: new Date() },
                endDate: { [Op.gte]: new Date() },
                everStarted: false
            }, async (promo) => {
                log.info("Start pending promotion", promo.toShortInfo());
                try {
                    const brand = await EntityCache.findOne({ id: promo.getBrandId() });
                    const result = await this.doStartPromotion(brand as BrandEntity, promo);
                    log.info("Pending promotion started", promo.getId(), result);
                } catch (err) {
                    log.error(err, "Failed to start pending promotion", promo.toShortInfo());
                }
            });
        });
    }

    private async doStartPromotion(brand: BrandEntity, promo: PromotionImpl): Promise<PromotionUpdateResult> {
        if (!promo.isRunning() || promo.hasFinished()) {
            return Promise.reject(new Errors.PromoIsNotInValidState());
        }

        const service = PlayerRewardServices.get(brand, promo.getType());
        const pending = await PlayerPromotionDb.getPromotionPlayers(promo.getId(),
            { status: PromotionToPlayerStatus.CONFIRMED });

        await service.start(promo, pending);

        const result = {};
        for (const promoPlayer of pending) {
            result[promoPlayer.playerCode] = "OK";
        }

        return result;
    }
}

export const PlayerPromotionService = new PlayerPromotionServiceImpl();

let job;

export function initCheckPromotionStartedJob(configSchedule) {
    if (job) {
        return;
    }
    job = schedule.scheduleJob(configSchedule, () => {
        return PlayerPromotionService.startPendingPromotions();
    });
}
