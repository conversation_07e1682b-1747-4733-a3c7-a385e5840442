import { ValidateNickname } from "../utils/validateNickname";
import { BindOrReplacements, literal, Op, WhereOptions } from "sequelize";
import * as FilterService from "./filter";
import { PagingHelper } from "../utils/paginghelper";
import config from "../config";
import { NicknameChangeExceededError, PlayerNotFoundError } from "../errors";
import { lazy } from "@skywind-group/sw-utils";
import { sequelize as db } from "../storage/db";
import { Models } from "../models/models";
import { PlayerInfo } from "../entities/playerInfo";

const sortableKeys = ["playerCode", "nickname", "isMerchantPlayer", "isVip", "createdAt"];
const DEFAULT_SORT_KEY = "playerCode";

export const queryParamsKeys = [
    "sortBy",
    "sortOrder",
    "offset",
    "limit",
    "playerCode",
    "brandId",
    "nickname",
    "isPrivateChatBlock",
    "hasWarn",
    "isPublicChatBlock"
];

export interface CreateOrUpdateOptions {
    isRealBrandIdNeeded?: boolean;
    increaseNicknameChangeAttempts?: boolean;
    isSentByGame?: boolean;
}

export class PlayerInfoService {
    public static model = Models.PlayerInfoModel;
    protected validator = new ValidateNickname(true);

    public async getPlayerInfo(playerCode: string,
                               brandId: number,
                               skipPlayerInfoRequest: boolean = false,
                               isLiveGame: boolean = true): Promise<PlayerInfo> {

        let playerInfo;
        if (playerCode) {
            if (isLiveGame || !skipPlayerInfoRequest) {
                playerInfo = await PlayerInfoService.model.findOne({
                    where: {
                        brandId: { [Op.eq]: brandId },
                        playerCode: { [Op.eq]: playerCode },
                    }
                });
            }
        }

        return playerInfo ?
               playerInfo.toInfo() :
               {
                   isPublicChatBlock: false,
                   isTracked: false,
                   isVip: false,
                   hasWarn: false,
                   isPrivateChatBlock: false,
                   noBetNoChat: false,
               } as PlayerInfo;
    }

    public async createOrUpdate(data: PlayerInfo, options: CreateOrUpdateOptions = {}): Promise<PlayerInfo> {
        const item = await PlayerInfoService.model.findOne({
            where: {
                brandId: { [Op.eq]: data.brandId },
                playerCode: { [Op.eq]: data.playerCode.toString() },
            }
        });

        const updateData: PlayerInfo & BindOrReplacements = {
            playerCode: data.playerCode,
            brandId: data.brandId,
            isMerchantPlayer: data.isMerchantPlayer !== undefined
                              ? data.isMerchantPlayer
                              : (item && item.isMerchantPlayer),
        };
        if (options.isSentByGame && data.noBetNoChat !== undefined) {
            updateData.noBetNoChat = data.noBetNoChat;
        }
        if (data.isPublicChatBlock !== undefined) {
            updateData.isPublicChatBlock = data.isPublicChatBlock;
        }
        if (data.isPrivateChatBlock !== undefined) {
            updateData.isPrivateChatBlock = data.isPrivateChatBlock;
        }
        if (data.isVip !== undefined) {
            updateData.isVip = data.isVip;
        }
        if (data.isTracked !== undefined) {
            updateData.isTracked = data.isTracked;
        }
        if (data.nickname) {
            if (options.increaseNicknameChangeAttempts && item) {
                if (++item.nicknameChangeAttempts > config.playerNicknameChangeAttemptsLimit) {
                    throw new NicknameChangeExceededError();
                }
                updateData.nicknameChangeAttempts = item.nicknameChangeAttempts;
            }
            await this.validator.checkSymbols(data.nickname);
            await this.validator.checkIdenticalUsername(data.nickname, data.playerCode);
            updateData.nickname = data.nickname;
        }
        if (data.hasWarn !== undefined) {
            updateData.hasWarn = data.hasWarn;
        }
        if (data.restrictedIpCountries !== undefined) {
            updateData.restrictedIpCountries = JSON.stringify(data.restrictedIpCountries) as any;
        }
        const [dbItem] = await db.query(
            this.createUpsertPlayerInfoQuery(updateData),
            {
                replacements: updateData,
                model: PlayerInfoService.model,
                instance: item,
                mapToModel: true
            }
        );
        return dbItem.toInfo(options.isRealBrandIdNeeded);
    }

    private createUpsertPlayerInfoQuery(updateData: PlayerInfo): string {
        const insertFields = [];
        const insertValues = [];
        const updateFields = [];
        for (const key in updateData) {
            if (updateData.hasOwnProperty(key)) {
                // Convert camelCase fields to snake_case
                const field = key.replace(/([A-Z])/g, letter => `_${letter.toLowerCase()}`);
                insertFields.push(`"${field}"`);
                insertValues.push(`:${key}`);
                updateFields.push(`"${field}" = COALESCE(:${key}, players_info."${field}")`);
            }
        }
        if (!insertFields.includes("\"created_at\"")) {
            insertFields.push("\"created_at\"");
            insertValues.push("NOW()");
        }
        if (!insertFields.includes("\"updated_at\"")) {
            insertFields.push("\"updated_at\"");
            insertValues.push("NOW()");
            updateFields.push("\"updated_at\" = NOW()");
        }
        return `
            INSERT INTO players_info (${insertFields.join(", ")})
            VALUES (${insertValues.join(", ")})
            ON CONFLICT (player_code, brand_id) 
            DO UPDATE SET ${updateFields.join(", ")}
            RETURNING players_info.*;
            `;
    }

    public async find(query: WhereOptions<any> = {},
                      brandId?: number,
                      isNeedRealBrandId?: boolean): Promise<PlayerInfo[]> {
        if (brandId) {
            query["brandId"] = brandId;
        }
        const sortBy = FilterService.getSortKey(query, sortableKeys, DEFAULT_SORT_KEY);
        const sortOrder = FilterService.valueFromQuery(query, "sortOrder") || "ASC";

        return PagingHelper.findAndCountAll(PlayerInfoService.model,
            {
                where: query,
                offset: FilterService.valueFromQuery(query, "offset"),
                limit: FilterService.valueFromQuery(query, "limit"),
                order: literal(`"${sortBy}" ${sortOrder}`),
                include: [
                    {
                        association: PlayerInfoService.model.associations.entity,
                        attributes: ["name", "title"]
                    }
                ],
            },
            player => ({
                playerCode: player.playerCode,
                brandId: isNeedRealBrandId ? `${player.brandId}` : player.brandId,
                isMerchantPlayer: player.isMerchantPlayer,
                nickname: player.nickname,
                isVip: player.isVip,
                isTracked: player.isTracked,
                isPublicChatBlock: player.isPublicChatBlock,
                isPrivateChatBlock: player.isPrivateChatBlock,
                hasWarn: player.hasWarn,
                brandTitle: player.get("entity").name || player.get("entity").title,
                nicknameChangeAttempts: player.nicknameChangeAttempts,
                restrictedIpCountries: player.restrictedIpCountries,
                noBetNoChat: player.noBetNoChat
            }));
    }

    public async resetNicknameChangeAttempts(playerCode: string, brandId: number | string) {
        const item = await PlayerInfoService.model.findOne({
            where: {
                brandId: brandId,
                playerCode: playerCode.toString()
            }
        });

        if (!item) {
            throw new PlayerNotFoundError();
        }
        return item.update({ nicknameChangeAttempts: 0 });
    }
}

const playerInfoService = lazy(() => new PlayerInfoService());

export function getPlayerInfoService(): PlayerInfoService {
    return playerInfoService.get();
}
