import { PlayerImpl } from "./brandPlayer";
import { BrandEntity } from "../entities/brand";
import { EntityGame, LobbyGameURLInfo, PlayerGameURLInfo } from "../entities/game";
import { getMerchantCRUDService, getMerchantService } from "./merchant";
import * as Errors from "../errors";
import { IPMGameInitRequest } from "@skywind-group/sw-management-adapters";
import { BaseEntity, FindEntityOptions } from "../entities/entity";
import { PlayerDBInstance } from "../models/player";
import { MerchantGameInitRequest, MerchantStartGameTokenData, PlayMode } from "@skywind-group/sw-wallet-adapter-core";
import { validatePlaymode } from "../utils/validatePlaymode";
import { getEntityGame } from "./entityGameService";
import logger from "../utils/logger";
import { generatePlayerLoginToken, PlayerLoginTokenData, verifyStartGameToken } from "../utils/token";
import { getGameURLInfo } from "./gameUrl/getGameURLInfo";
import { EntitySettings } from "../entities/settings";
import { getEntitySettings } from "./settings";
import { getGameGroupService } from "./gamegroup";
import { ContextVariables } from "../utils/contextVariables";
import { toLobbyGameInfo } from "./lobby/parseMenuItems";
import { Models } from "../models/models";
import * as MerchantCache from "../cache/merchant";

const log = logger("get-player-brand-game-url");
const PlayerModel = Models.PlayerModel;
const GameGroupModel = Models.GameGroupModel;

type GetPlayerGameURLConfig = PlayerGameURLConfig & FindEntityConfig;

interface FindEntityConfig {
    keyEntity: BaseEntity;
    options?: FindEntityOptions;
}

interface PlayerGameURLConfig {
    playerCode: string;
    gameCode: string;
    playMode: PlayMode;
    ip?: string;
    language?: string;
    ticket?: string;
    lobby?: string;
    cashier?: string;
    ignoreWebSiteWhitelistedCheck?: boolean;
    lobbySessionId?: string;
    aamsSessionId?: string;
    aamsParticipationCode?: string;
    isExternalLogin?: boolean; // Used for merchant, brand-merchant
}

export type GetAnonymousGameURLConfig = AnonymousGameURLConfig & FindEntityConfig;

interface AnonymousGameURLConfig {
    gameCode?: string;
    ip?: string;
    playerCode?: string;
    language?: string;
    ticket?: string;
    merchantLoginUrl?: string;
    lobby?: string;
    cashier?: string;
    ignoreWebSiteWhitelistedCheck?: boolean;
    gameGroup?: string;
    currency?: string;
}

export async function getLobbyPlayerGameURL(entity: BrandEntity,
                                            tokenData: PlayerLoginTokenData,
                                            gameCode: string,
                                            ip?: string): Promise<LobbyGameURLInfo> {
    if (entity.isMerchant && (tokenData.customerSessionId || tokenData.isExternalTerminal)) {
        const merchant = await getMerchantCRUDService().findOne(entity);
        validatePlaymode(entity, tokenData.playMode, merchant);

        const entitySettings = await getEntitySettings(entity.path);
        const entityGame = await getMerchantService().getEntityGame(merchant, gameCode, tokenData.playerCode, entitySettings.cacheEntityGames);

        const providerGameCode = entityGame?.game?.providerGameCode;

        const startTokenData: MerchantStartGameTokenData = {
            ...tokenData,
            merchantType: merchant.type,
            merchantCode: merchant.code,
            gameCode,
            providerGameCode,
            providerCode: entityGame?.game?.gameProvider?.code,
            currency: tokenData.currency
        };

        const urlInfo = await getGameURLInfo({
            entityGame,
            brand: entity,
            entitySettings,
            disableLauncher: false,
            ignoreWebSiteWhitelistedCheck: true,
            merchant,
            player: {
                code: tokenData.playerCode,
                currency: tokenData.currency,
                language: tokenData.language,
                country: tokenData.country,
                gameGroup: tokenData.gameGroup,
                isTest: tokenData.test
            },
            isLobby: true,
            request: {
                ...startTokenData,
                ip,
                previousStartTokenData: {
                    ...startTokenData,
                    gameCode: tokenData.gameCode
                },
                lobbySessionId: tokenData.sessionId
            }
        });

        return {
            ...urlInfo,
            newPlayerToken: await refreshToken(urlInfo.token, tokenData),
            game: toLobbyGameInfo(entityGame, {})
        };
    }
    const config: PlayerGameURLConfig = {
        playerCode: tokenData.playerCode,
        gameCode,
        playMode: tokenData.playMode,
        ip,
        lobbySessionId: tokenData.sessionId,
        ignoreWebSiteWhitelistedCheck: true,
        lobby: tokenData.lobby,
        cashier: tokenData.cashier,
        aamsSessionId: tokenData.aamsSessionId,
        aamsParticipationCode: tokenData.aamsParticipationCode,
        isExternalLogin: tokenData.isExternalTerminal
    };

    return getBrandPlayerGameURL(entity, config, true);
}

async function refreshToken(newGameToken: string, tokenData: PlayerLoginTokenData): Promise<string> {
    const parsedNewGameToken = newGameToken ? await verifyStartGameToken(newGameToken) : {} as any;
    const mergedOldAndNewTokenData = {
        ...tokenData,
        ...parsedNewGameToken
    };

    mergedOldAndNewTokenData["externalGameId"] = parsedNewGameToken.externalGameId || undefined;

    const { exp, iss, ias, ...cleanedTokenData } = mergedOldAndNewTokenData;

    return generatePlayerLoginToken(cleanedTokenData);
}

export async function getPlayerGameURL(config: GetPlayerGameURLConfig): Promise<PlayerGameURLInfo> {
    const brand = findBrandEntity(config.keyEntity, config.options);
    if (brand.isMerchant) {
        return getMerchantPlayerGameURL(brand, config);
    }
    return getBrandPlayerGameURL(brand, config);
}

async function getMerchantPlayerGameURL(brand: BrandEntity,
                                        config: PlayerGameURLConfig,
                                        isLobby = false): Promise<PlayerGameURLInfo> {
    if (!config.ticket) {
        return Promise.reject(new Errors.ValidationError("Ticket is required for Merchants"));
    }

    const merchant = await MerchantCache.findOne(brand);
    const merchantGameParameters: MerchantGameInitRequest = {
        merchantType: merchant.type,
        merchantCode: merchant.code,
        gameCode: config.gameCode,
        playmode: config.playMode,
        language: config.language,
        ticket: config.ticket,
        lobby: config.lobby,
        cashier: config.cashier,
        aamsSessionId: config.aamsSessionId,
        aamsParticipationCode: config.aamsParticipationCode
    };

    return getMerchantService().getGameUrl(merchantGameParameters, {
        ip: config.ip,
        ignoreWebSiteWhitelistedCheck: config.ignoreWebSiteWhitelistedCheck,
        isLobby
    }, merchant);
}

async function getBrandPlayerGameURL(brand: BrandEntity,
                                     config: PlayerGameURLConfig,
                                     isLobby = false): Promise<LobbyGameURLInfo> {
    validatePlaymode(brand, config.playMode);

    const playerInstance = await findPlayer(config.playerCode, brand.id);
    const player = new PlayerImpl(playerInstance);

    let entityGame: EntityGame;
    try {
        validatePlayerSuspended(player);
        entityGame = await getEntityGame(brand, config.gameCode, true);
    } catch (err) {
        log.error(err.message, JSON.stringify({
            gameCode: config.gameCode,
            playerCode: config.playerCode,
            playerId: player.id,
            entityId: brand.id,
            errorCode: err.code
        }));
        return Promise.reject(err);
    }

    const entitySettings: EntitySettings = await getEntitySettings(brand.path);
    const urlInfo = await getGameURLInfo({
        entityGame: entityGame,
        brand: brand,
        entitySettings: entitySettings,
        disableLauncher: false,
        ignoreWebSiteWhitelistedCheck: config.ignoreWebSiteWhitelistedCheck,
        merchant: null,
        player,
        isLobby,
        request: {
            ip: config.ip,
            playMode: config.playMode || PlayMode.REAL,
            platform: "desktop",
            language: config.language,
            lobby: config.lobby,
            cashier: config.cashier,
            lobbySessionId: config.lobbySessionId,
            isExternalLogin: config.isExternalLogin
        }
    });

    // Logging Last player success get token
    // With silent: true the updatedAt timestamp will not be updated.
    // Note: Silent work only in case: update(keys: Object, options?: InstanceUpdateOptions)
    // without "await"
    playerInstance.update({ lastLogin: new Date() });

    return {
        ...urlInfo,
        ...(isLobby ? { game: toLobbyGameInfo(entityGame, {}) } : {})
    };
}

async function validateAnonymousGameURLConfig(entity: BaseEntity, config: GetAnonymousGameURLConfig) {
    if (!entity) {
        return Promise.reject(new Errors.EntityCouldNotBeFound());
    }
    if (entity.isSuspended()) {
        return Promise.reject(new Errors.ParentSuspendedError());
    }
    if (config.currency && !entity.getCurrencies().includes(config.currency)) {
        return Promise.reject(new Errors.CurrencyNotInListError(config.currency));
    }
    if (config.language && !entity.getLanguages().includes(config.language)) {
        return Promise.reject(new Errors.LanguageNotInListError(config.language));
    }
    if (config.gameGroup) {
        await getGameGroupService().findOne(entity, { name: config.gameGroup }, true);
    }
}

async function removeLanguageAndGameGroupIncorrectValues(entity: BaseEntity, config: GetAnonymousGameURLConfig) {
    if (config.language !== undefined && !entity.getLanguages().includes(config.language)) {
        config.language = undefined; // fallback on the default language
    }
    if (config.gameGroup) {
        try {
            await getGameGroupService().findOne(entity, { name: config.gameGroup }, true);
        } catch (e) {
            if (e.code === 211) { // Game group is not found
                config.gameGroup = undefined; // fallback on default game group
            }
        }
    }
}

export async function getAnonymousGameURL(config: GetAnonymousGameURLConfig): Promise<PlayerGameURLInfo> {
    const entity: BaseEntity = config.options ? config.keyEntity.find(config.options) : config.keyEntity;
    await removeLanguageAndGameGroupIncorrectValues(entity, config);
    await validateAnonymousGameURLConfig(entity, config);
    const brandEntity = entity as BrandEntity;
    const brand = config.keyEntity as BrandEntity;
    ContextVariables.setEntityId(brand.id);
    if (brandEntity.isMerchant && (config.ticket || config.merchantLoginUrl)) {
        return getMerchantAnonymousGameURL(brand, config);
    } else {
        return getBrandAnonymousGameURL(brand, config);
    }
}

async function getMerchantAnonymousGameURL(brand: BrandEntity,
                                           config: AnonymousGameURLConfig): Promise<PlayerGameURLInfo> {
    const merchant = await getMerchantCRUDService().findOne(brand);
    const merchantGameParameters: IPMGameInitRequest = {
        merchantType: merchant.type,
        merchantCode: merchant.code,
        gameCode: config.gameCode,
        playmode: PlayMode.FUN,
        language: config.language || brand.defaultLanguage,
        merch_login_url: config.merchantLoginUrl,
        ticket: config.ticket,
        gameGroup: config.gameGroup,
        currency: config.currency
    };
    return getMerchantService().getGameUrl(merchantGameParameters, config);
}

async function getBrandAnonymousGameURL(brand: BrandEntity,
                                        config: AnonymousGameURLConfig): Promise<PlayerGameURLInfo> {
    let entityGame: EntityGame;
    try {
        entityGame = await getEntityGame(brand, config.gameCode, true);
    } catch (err) {
        log.error(err.message, JSON.stringify({
            gameCode: config.gameCode,
            playerCode: config.playerCode,
            playerId: "Anonymous",
            entityId: brand.id,
            errorCode: err.code
        }));
        return Promise.reject(err);
    }
    return getGameURLInfo({
        entityGame: entityGame,
        brand: brand,
        entitySettings: await getEntitySettings(brand.path),
        disableLauncher: false,
        ignoreWebSiteWhitelistedCheck: config.ignoreWebSiteWhitelistedCheck,
        merchant: null,
        player: {
            code: config.playerCode || `player${Date.now()}`,
            currency: config.currency || brand.defaultCurrency || "USD",
            language: config.language || brand.defaultLanguage || "en",
            isTest: true,
            gameGroup: config.gameGroup
        },
        request: {
            ip: config.ip,
            playMode: PlayMode.FUN,
            platform: "desktop",
            lobby: config.lobby,
            cashier: config.cashier
        }
    });
}

/**
 * Search player by playerCode and brandId, throws PlayerNotFoundError if not found.
 * @throws PlayerNotFoundError
 */
export async function findPlayer(playerCode: string, brandId: number): Promise<PlayerDBInstance> {
    const playerInstance: PlayerDBInstance = await PlayerModel.findOne({
        include: [
            {
                model: GameGroupModel,
            },
        ],
        where: {
            brandId: brandId,
            code: playerCode,
        },
    });

    if (!playerInstance) {
        return Promise.reject(new Errors.PlayerNotFoundError());
    }

    return playerInstance;
}

/**
 * Return BrandEntity or throw errors if could't find, if this entity is not brand and if brand is suspended
 * @throws EntityCouldNotBeFound
 * @throws NotBrand
 * @throws ParentSuspendedError
 */
export function findBrandEntity(keyEntity: BaseEntity, options?: FindEntityOptions): BrandEntity {

    const entity: BaseEntity = options ? keyEntity.find(options) : keyEntity;
    if (!entity) {
        throw new Errors.EntityCouldNotBeFound();
    }
    if (!entity.isBrand()) {
        throw new Errors.NotBrand();
    }
    const brand: BrandEntity = entity as BrandEntity;

    if (brand.isSuspended()) {
        throw new Errors.ParentSuspendedError();
    }

    return brand;
}

function validatePlayerSuspended(player) {
    if (player.status === "suspended") {
        throw new Errors.PlayerIsSuspended();
    }
}
