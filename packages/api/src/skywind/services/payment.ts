import {
    FindOptions,
    PaymentMethod,
    PaymentMethodCreateData,
    PaymentMethodInfo,
    PaymentMethodUpdateData
} from "../entities/payment_method";
import { PaymentMethodDBInstance } from "../models/payment_method";
import * as Errors from "../errors";
import { literal, Op, WhereOptions } from "sequelize";
import { BaseEntity } from "../entities/entity";

import {
    APPROVED,
    DECLINED,
    DirectTransferData,
    INIT,
    OrderInfo,
    OrderStatus,
    PM_TYPE_DEPOSIT,
    PM_TYPE_WITHDRAW,
    STATUS_NORMAL,
    STATUS_SUSPENDED,
    TRANSFER_IN,
    TRANSFER_OUT,
    TransferData
} from "../entities/payment";
import { PaymentAttributes, PaymentDBInstance, PaymentDeclineReason } from "../models/payment";
import * as FilterService from "../services/filter";
import { Player } from "../entities/player";
import { BrandEntity } from "../entities/brand";
import {
    BAD_TRANSACTION_ID,
    IChangeInfo,
    INSUFFICIENT_BALANCE,
    ITransaction,
    MAX_CAPACITY_REACHED,
    TRANSACTION_EXISTS,
} from "@skywind-group/sw-wallet";
import { Currencies } from "@skywind-group/sw-currency-exchange";
import { PATH_CHAR, validateMaintenance } from "./entity";
import PlayerResponsibleGamingServiceImpl from "./playerResponsibleGaming";
import logger from "../utils/logger";
import { OPERATION_ID, PlayerWalletImpl, WalletErrors, WalletFacade } from "@skywind-group/sw-management-wallet";
import { CronJob } from "../utils/cronJob";
import config from "../config";
import { Lazy, lazy } from "@skywind-group/sw-utils";
import { findBrandEntity } from "./urlManager";
import { executeLongQuery, sequelizeSlave } from "../storage/db";
import { PlayerPromotionWalletFacade } from "@skywind-group/sw-management-promo-wallet";
import { EntitySettings } from "../entities/settings";
import { getEntitySettings } from "./settings";
import { auditAsync, AuditInfo, auditParam, masterAudit } from "../utils/auditHelper";
import { ACTION_METHOD } from "../utils/common";
import { getBrandPlayerValidator } from "./brandPlayerValidator";
import { getBrandPlayerService } from "./brandPlayer";
import { Models } from "../models/models";

const log = logger("payment-service");

const PaymentModel = Models.PaymentMethodModel;
const PaymentOrderModel = Models.PaymentModel;
const PaymentOrderSlaveModel = Models.PaymentSlaveModel;

const DEFAULT_SORT_KEY = "startDate";

export const queryParamsKeys = [
    "extTrxId",
    "trxId",
    "playerCode",
    "orderId",
    "currencyCode",
    "paymentMethodCode",
    "amount",
    "startDate",
    "endDate",
    "status",
    "isTest",
    "sortBy",
    "sortOrder",
    "offset",
    "limit",
    "orderStatus",
    "orderType",
    "isTest"
];

const sortableKeys = [
    "trxId",
    "playerCode",
    "currencyCode",
    "paymentMethodCode",
    "amount",
    "startDate",
    "endDate",
    "orderStatus",
    "isTest",
];

export class PaymentMethodImpl implements PaymentMethod {
    public id: number;
    public brandId: number;
    public type: Array<string>;
    public code: string;
    public name: string;
    public description: string;
    public status: string;

    constructor(item?) {
        if (!item) {
            return;
        }

        this.id = item.get("id");
        this.brandId = item.get("brandId");
        this.type = item.get("type");
        this.code = item.get("code");
        this.name = item.get("name");
        this.description = item.get("description");
        this.status = item.get("status");
    }

    public isSuspended(): boolean {
        return this.status === STATUS_SUSPENDED;
    }

    public toInfo(): PaymentMethodInfo {
        return {
            type: this.type,
            code: this.code,
            name: this.name,
            description: this.description,
            status: this.status,
        };
    }
}

export async function create(data: PaymentMethodCreateData): Promise<PaymentMethodInfo> {
    const paymentMethod: PaymentMethodImpl = new PaymentMethodImpl();

    if (!data.type.find(item => item === PM_TYPE_DEPOSIT) && !data.type.find(item => item === PM_TYPE_WITHDRAW)) {
        return Promise.reject(new Errors.PaymentMethodTypeNotFoundError());
    }

    paymentMethod.brandId = data.brandId;
    paymentMethod.type = data.type;
    paymentMethod.code = data.code;
    paymentMethod.name = data.name;
    paymentMethod.description = data.description;
    paymentMethod.status = data.status || STATUS_NORMAL;

    try {
        const item: PaymentMethodDBInstance = await PaymentModel.create(paymentMethod);
        return new PaymentMethodImpl(item).toInfo();
    } catch (err) {
        return Promise.reject(new Errors.PaymentMethodBadBrandId());
    }
}

export async function remove(entity: BaseEntity, name: string): Promise<number> {
    return PaymentModel.destroy({
        where: {
            brandId: entity.id,
            name: name,
        },
    });
}

export async function findOne(entity: BaseEntity, code: string): Promise<PaymentMethod> {
    const item = await PaymentModel.findOne({ where: { brandId: entity.id, code: code } });
    return item ? new PaymentMethodImpl(item) : Promise.reject(new Errors.PaymentMethodNotFoundError());
}

export async function find(entity: BaseEntity, options: FindOptions): Promise<PaymentMethodInfo[]> {
    const where: WhereOptions<any> = {
        type: { [Op.contains]: options.type },
        brandId: entity.id,
    };
    if (options.status) {
        where["status"] = options.status;
    }
    return PaymentModel.findAll({ "where": where })
        .then(items => items.map(item => (new PaymentMethodImpl(item)).toInfo()));
}

export async function update(entity: BaseEntity,
                             code: string,
                             data: PaymentMethodUpdateData): Promise<PaymentMethodInfo> {
    const paymentMethod: PaymentMethod = await findOne(entity, code);

    if (!data.type.find(item => item === PM_TYPE_DEPOSIT) && !data.type.find(item => item === PM_TYPE_WITHDRAW)) {
        return Promise.reject(new Errors.PaymentMethodTypeNotFoundError());
    }

    paymentMethod.name = data.name ? data.name : paymentMethod.name;
    paymentMethod.type = data.type ? data.type : paymentMethod.type;
    paymentMethod.status = data.status ? data.status : paymentMethod.status;
    paymentMethod.description = data.description ? data.description : paymentMethod.description;

    await PaymentModel.update(paymentMethod, {
        where: {
            id: paymentMethod.id,
        },
    });
    return paymentMethod.toInfo();
}

export const getEntityPlayerFinanceService = (auditInfo?: AuditInfo): EntityPlayerFinanceService => {
    return new EntityPlayerFinanceServiceImpl(auditInfo);
};

export interface EntityPlayerFinanceService {
    transferIn(brand: BrandEntity, data: TransferData): Promise<OrderInfo>;

    transferOut(brand: BrandEntity, data: TransferData): Promise<OrderInfo>;

    checkStatusAndGetPaymentOrder(entityId: number, trxId: string, extTransactionId?: string): Promise<OrderInfo>;

    checkStatusAndGetPaymentOrderById(id: number): Promise<OrderInfo>;

    transferOutFromPlayerToParent(parent: BaseEntity, data: DirectTransferData): Promise<OrderInfo>;

    transferInToPlayerFromParent(parent: BaseEntity, data: DirectTransferData): Promise<OrderInfo>;

    ensurePlayerIsOffline(player: Player): Promise<void>;
}

export class PaymentOrderImpl {
    public id: number;
    public trxId: string;
    public extTrxId: string;
    public brandId: string;
    public brandTitle: string;
    public playerCode: string;
    public orderId: string;
    public orderType: string;
    public orderDate: Date;
    public orderInfo: any;
    public orderStatus: OrderStatus;
    public currencyCode: string;
    public amount: number;
    public playerBalanceAfter: number;
    public paymentMethodCode: string;
    public startDate: Date;
    public endDate: Date;
    public processedBy: string;
    public marks: any;
    public isTest: boolean;
    public declineReason: PaymentDeclineReason;

    constructor(item?, entity?: BaseEntity) {
        if (!item) {
            return;
        }

        this.id = item.get("id");
        this.trxId = item.get("trxId");
        this.extTrxId = item.get("extTrxId");
        this.brandId = item.get("brandId");
        this.playerCode = item.get("playerCode");
        this.orderId = item.get("orderId");
        this.orderType = item.get("orderType");
        this.orderDate = item.get("orderDate");
        this.orderInfo = item.get("orderInfo");
        this.orderStatus = item.get("orderStatus");
        this.currencyCode = item.get("currencyCode");
        this.amount = +item.get("amount");
        this.playerBalanceAfter = +item.get("playerBalanceAfter");
        this.paymentMethodCode = item.get("paymentMethodCode");
        this.startDate = item.get("startDate");
        this.endDate = item.get("endDate");
        this.processedBy = item.get("processedBy");
        this.marks = item.get("marks");
        this.isTest = item.get("isTest");
        this.brandTitle = entity ? entity.title : undefined;
        this.declineReason = item.get("declineReason");
    }

    public toInfo(): OrderInfo {
        return {
            trxId: this.trxId,
            extTrxId: this.extTrxId,
            brandId: +this.brandId,
            brandTitle: this.brandTitle,
            playerCode: this.playerCode,
            orderId: this.orderId,
            orderType: this.orderType,
            orderDate: this.orderDate ? this.orderDate.toISOString() : null,
            orderInfo: this.orderInfo,
            orderStatus: this.orderStatus,
            currencyCode: this.currencyCode,
            amount: this.amount,
            playerBalanceAfter: this.playerBalanceAfter !== null ? this.playerBalanceAfter : undefined,
            startDate: this.startDate ? this.startDate.toISOString() : null,
            endDate: this.endDate ? this.endDate.toISOString() : null,
            paymentMethodCode: this.paymentMethodCode,
            declineReason: this.declineReason !== null ? this.declineReason : undefined,
            isTest: this.isTest
        };
    }
}

export class EntityPlayerFinanceServiceImpl implements EntityPlayerFinanceService {
    // auditInfo is used for directives
    constructor(private auditInfo?: AuditInfo) {
    }

    public async transferIn(brand: BrandEntity, data: TransferData): Promise<OrderInfo> {

        const { player, paymentData, isNew } = await this.validatePlayerAndGetPaymentData(brand, data, TRANSFER_IN);
        await new PlayerResponsibleGamingServiceImpl(brand).validatePlayerDepositRestriction(data.playerCode,
            data.amount);

        if (paymentData.get("orderStatus") === INIT) {
            let trxResult: IChangeInfo[];
            let transaction: ITransaction;
            try {
                transaction = await WalletFacade.startTransactionWithID(paymentData.get("trxId"),
                    {
                        operationId: OPERATION_ID.BET,
                        operationName: TRANSFER_IN,
                        externalTrxId: data.extTrxId,
                        params: { isTest: data.isTest === undefined ? false : data.isTest }
                    });

                await brand.wallet.debit(transaction, data.currency, data.amount);
                await player.wallet.deposit(transaction, data);
            } catch (err) {
                return this.handleTransferError(err, paymentData, isNew);
            }

            try {
                trxResult = await transaction.commit();
            } catch (err) {
                return this.handleTrxCommitError(err, paymentData, isNew);
            }

            await this.markOrderAsApproved(paymentData, trxResult);
        } else {
            this.trackChanges(paymentData.toJSON(), data);
        }

        const orderInfo: OrderInfo = (new PaymentOrderImpl(paymentData)).toInfo();
        orderInfo.isNew = isNew;
        return orderInfo;
    }

    public async transferOut(brand: BrandEntity,
                             data: TransferData): Promise<OrderInfo> {

        const { player, paymentData, isNew } = await this.validatePlayerAndGetPaymentData(brand, data, TRANSFER_OUT);

        if (paymentData.get("orderStatus") === INIT) {
            let trxResult: IChangeInfo[];
            let transaction: ITransaction;
            try {
                transaction = await WalletFacade.startTransactionWithID(paymentData.get("trxId"),
                    {
                        operationId: OPERATION_ID.BET,
                        operationName: TRANSFER_OUT,
                        externalTrxId: data.extTrxId,
                        params: { isTest: data.isTest === undefined ? false : data.isTest }
                    });

                await brand.wallet.credit(transaction, data.currency, data.amount);
                await player.wallet.withdraw(transaction, data);

                // withdraw played free bets from operator
                const freebetPromoWallet = await new PlayerPromotionWalletFacade(player.wallet)
                    .getFreebetWallet(transaction);
                const freeBetAmount = await freebetPromoWallet.reduceTotalFreeBetAmount(data.currency);
                await brand.wallet
                    .debitFreeBetAmount(transaction, data.currency, freeBetAmount);
            } catch (err) {
                return this.handleTransferError(err, paymentData, isNew, false);
            }

            try {
                trxResult = await transaction.commit();
            } catch (err) {
                return this.handleTrxCommitError(err, paymentData, isNew, false);
            }

            await this.markOrderAsApproved(paymentData, trxResult);
        } else {
            this.trackChanges(paymentData.toJSON(), data);
        }

        const orderInfo: OrderInfo = (new PaymentOrderImpl(paymentData)).toInfo();
        orderInfo.isNew = isNew;
        return orderInfo;
    }

    private trackChanges(payment: PaymentAttributes, data: TransferData): void {
        const { amount, currencyCode, playerCode } = payment;
        if (+amount !== +data.amount ||
            currencyCode !== data.currency ||
            playerCode !== data.playerCode) {

            let info = `Payment has difference between previous and current transfer data (trxId: ${payment.trxId}): `;
            info +=
            (+amount !== +data.amount) ? `saved amount ${data.amount} != new amount ${amount} ` : "" +
            (currencyCode !== data.currency) ? `saved currency ${data.currency} != new currency ${currencyCode} ` : "" +
            (playerCode !== data.playerCode) ? `saved playerCode ${data.playerCode} != new playerCode ${playerCode}` : "";

            log.warn({
                    previous: {
                        amount,
                        currency: currencyCode,
                        playerCode
                    },
                    current: data
                },
                info);
            throw new Errors.SameTrxIdDifferentTransactionData(info);
        }

    }

    public async checkStatusAndGetPaymentOrder(entityId: number,
                                               trxId: string,
                                               extTransactionId?: string): Promise<OrderInfo> {
        const query: WhereOptions<any> = {
            brandId: entityId.toString()
        };
        if (trxId) {
            query.trxId = trxId;
        }
        if (extTransactionId) {
            query.extTrxId = extTransactionId;
        }
        const paymentData: PaymentDBInstance = await PaymentOrderModel.findOne({ where: query });
        if (!paymentData) {
            return;
        }

        await this.checkOrderStatus(paymentData);

        return new PaymentOrderImpl(paymentData).toInfo();
    }

    public async checkStatusAndGetPaymentOrderById(id: number): Promise<OrderInfo> {
        const paymentData: PaymentDBInstance = await PaymentOrderModel.findByPk(id);
        if (!paymentData) {
            return;
        }

        await this.checkOrderStatus(paymentData);

        return new PaymentOrderImpl(paymentData).toInfo();
    }

    public async transferInToPlayerFromParent(parent: BaseEntity, data: DirectTransferData): Promise<OrderInfo> {
        const brand: BrandEntity = await getBrand(parent, data.brandPath);
        const { player, paymentData, isNew } = await this.validatePlayerAndGetPaymentData(brand, data, TRANSFER_IN);
        await new PlayerResponsibleGamingServiceImpl(brand).validatePlayerDepositRestriction(data.playerCode,
            data.amount);
        if (paymentData.get("orderStatus") === INIT) {
            let trxResult: IChangeInfo[];
            let transaction: ITransaction;
            try {
                transaction = await WalletFacade.startTransactionWithID(paymentData.get("trxId"),
                    {
                        operationId: OPERATION_ID.BET,
                        operationName: TRANSFER_IN,
                        externalTrxId: data.extTrxId,
                        params: { isTest: data.isTest === undefined ? false : data.isTest }
                    });

                await parent.wallet.debit(transaction, data.currency, data.amount);
                await brand.wallet.credit(transaction, data.currency, data.amount);
                await brand.wallet.debit(transaction, data.currency, data.amount);
                await player.wallet.deposit(transaction, data);
            } catch (err) {
                return this.handleTransferError(err, paymentData, isNew);
            }

            try {
                trxResult = await transaction.commit();
            } catch (err) {
                return this.handleTrxCommitError(err, paymentData, isNew);
            }

            await this.markOrderAsApproved(paymentData, trxResult);
        }

        const orderInfo: OrderInfo = (new PaymentOrderImpl(paymentData)).toInfo();
        orderInfo.isNew = isNew;
        return orderInfo;
    }

    public async transferOutFromPlayerToParent(parent: BaseEntity, data: DirectTransferData): Promise<OrderInfo> {
        const brand: BrandEntity = await getBrand(parent, data.brandPath);
        const { player, paymentData, isNew } = await this.validatePlayerAndGetPaymentData(brand, data, TRANSFER_OUT);
        if (paymentData.get("orderStatus") === INIT) {
            let trxResult: IChangeInfo[];
            let transaction: ITransaction;
            try {
                transaction = await WalletFacade.startTransactionWithID(paymentData.get("trxId"),
                    {
                        operationId: OPERATION_ID.BET,
                        operationName: TRANSFER_OUT,
                        externalTrxId: data.extTrxId,
                        params: { isTest: data.isTest === undefined ? false : data.isTest }
                    });

                await player.wallet.withdraw(transaction, data);
                await brand.wallet.credit(transaction, data.currency, data.amount);
                await brand.wallet.debit(transaction, data.currency, data.amount);
                await parent.wallet.credit(transaction, data.currency, data.amount);

                // withdraw played free bets from operator
                const freeBetPromoWallet = await new PlayerPromotionWalletFacade(player.wallet)
                    .getFreebetWallet(transaction);
                const freeBetAmount = await freeBetPromoWallet.reduceTotalFreeBetAmount(data.currency);
                await brand.wallet.debitFreeBetAmount(transaction, data.currency, freeBetAmount);
            } catch (err) {
                return this.handleTransferError(err, paymentData, isNew, false);
            }

            try {
                trxResult = await transaction.commit();
            } catch (err) {
                return this.handleTrxCommitError(err, paymentData, isNew, false);
            }

            await this.markOrderAsApproved(paymentData, trxResult);
        }

        const orderInfo: OrderInfo = (new PaymentOrderImpl(paymentData)).toInfo();
        orderInfo.isNew = isNew;
        return orderInfo;
    }

    private async checkOrderStatus(paymentData: PaymentDBInstance): Promise<void> {
        if (paymentData.get("orderStatus") === INIT) {
            const trxId = paymentData.get("trxId");
            try {
                const transaction = await WalletFacade.findCommittedTransaction(trxId, OPERATION_ID.BET);
                if (transaction) {
                    await this.markOrderAsApproved(paymentData, transaction.data);
                } else {
                    const trxTimeout = paymentData.get("createdAt").getTime() + config.paymentStatusJob.paymentTimeout;
                    if (Date.now() > trxTimeout) {
                        await this.markOrderAsDeclined(paymentData, PaymentDeclineReason.TRANSACTION_NOT_FOUND);
                    }
                }
            } catch (err) {
                // ignore error while attempting to mark order as approved
                log.warn(err, "Failed to mark order as approved/declined");
            }
        }
    }

    @auditAsync({
        summary: "Payment status update: mark order as approved"
    })
    private async markOrderAsApproved(@auditParam({ name: "payment", extractor: paymentAuditExtractor(APPROVED) })
                                          paymentData: PaymentDBInstance,
                                      trxResult: IChangeInfo[]): Promise<void> {
        try {
            const currencyCode = paymentData.get("currencyCode");
            const walletKey = PlayerWalletImpl.toWalletKey(+paymentData.get("brandId"),
                paymentData.get("playerCode"), currencyCode);
            const state = PlayerWalletImpl.parseTrxResult(walletKey, trxResult);
            if (state.currentValue !== undefined) {
                const currency = Currencies.get(currencyCode);
                paymentData.set("playerBalanceAfter", currency.toMajorUnits(state.currentValue));
            }
            paymentData.set("orderStatus", APPROVED);
            await paymentData.save();
        } catch (err) {
            log.error(err, "Failed to set Approved status for successful transfer", paymentData.get("extTrxId"));
            return Promise.reject(err);
        }
    }

    private async getOrCreatePaymentOrderDBItem(data: TransferData,
                                                brand: BrandEntity,
                                                player: Player,
                                                transferType: "transfer_in" | "transfer_out"):
        Promise<{ dbInstance: PaymentDBInstance, isNew: boolean }> {

        let isNew: boolean = false;

        let paymentData = await PaymentOrderModel.findOne({
            where: {
                extTrxId: { [Op.eq]: data.extTrxId.toString() },
                brandId: { [Op.eq]: brand.id.toString() }
            }
        });

        if (!paymentData) {
            paymentData = await this.createPaymentOrderModel(data, brand, player, transferType);
            isNew = true;
        }

        return {
            dbInstance: paymentData,
            isNew
        };
    }

    private async getAndValidatePlayer(brand: BrandEntity, data: TransferData): Promise<Player> {
        validateMaintenance(brand);

        const player: Player = await getBrandPlayerService().findOne(brand, { code: data.playerCode });
        if (player.currency !== data.currency) {
            return Promise.reject(new Errors.CurrencyMismatch());
        }
        getBrandPlayerValidator().validatePlayerSuspended(player);

        return player;
    }

    private async createPaymentOrderModel(data: TransferData, brand: BrandEntity, player: Player,
                                          transferType: "transfer_in" | "transfer_out"): Promise<PaymentDBInstance> {

        const newTrxId = await WalletFacade.generateTransactionId();
        const newPaymentData: PaymentAttributes = {
            trxId: newTrxId,
            extTrxId: data.extTrxId,
            brandId: brand.id.toString(),
            playerCode: player.code,
            orderType: transferType,
            currencyCode: data.currency,
            amount: data.amount,
            orderStatus: INIT,
            orderDate: new Date(),
            startDate: new Date(),
            endDate: new Date(),
            isTest: data.isTest,
        };

        return PaymentOrderModel.create(newPaymentData);
    }

    /**
     * Handler for error on transfer operation. Should return existing order info if transaction exists or mark order as
     * DECLINED and reject with error;
     */
    private async handleTransferError(err,
                                      paymentData: PaymentDBInstance,
                                      isNew: boolean,
                                      isTransferIn: boolean = true): Promise<OrderInfo> {
        if (err === TRANSACTION_EXISTS) {
            return this.handleTrxExistsError(paymentData, isNew);
        }

        log.error(err, "Failed to perform payment transfer", paymentData.get("extTrxId"));

        await this.markOrderAsDeclined(paymentData, this.mapPaymentDeclineReason(err, isTransferIn));

        return Promise.reject(this.mapTrxCommitError(err, isTransferIn));
    }

    /**
     * Handler for transfer transaction commit. Should return existing order info if transaction exists or
     * leave order in INIT state for unknown error and reject;
     */
    private async handleTrxCommitError(err,
                                       paymentData: PaymentDBInstance,
                                       isNew: boolean,
                                       isTransferIn: boolean = true): Promise<OrderInfo> {
        if (err === TRANSACTION_EXISTS) {
            return this.handleTrxExistsError(paymentData, isNew);
        }

        log.error(err, "Failed to perform payment transfer", paymentData.get("extTrxId"));

        if (this.isKnownWalletError(err)) {
            await this.markOrderAsDeclined(paymentData, this.mapPaymentDeclineReason(err, isTransferIn));
        }

        return Promise.reject(this.mapTrxCommitError(err, isTransferIn));
    }

    private async handleTrxExistsError(paymentData: PaymentDBInstance, isNew: boolean): Promise<OrderInfo> {
        try {
            paymentData.set("orderStatus", APPROVED);
            await paymentData.save();
            const existingOrderInfo: OrderInfo = (new PaymentOrderImpl(paymentData)).toInfo();
            existingOrderInfo.isNew = isNew;
            return existingOrderInfo;
        } catch (err) {
            log.error(err, "Failed to set Approved status for existing payment", paymentData.get("extTrxId"));
            // continue with the unknown error
            return Promise.reject(err);
        }
    }

    private mapTrxCommitError(err, isTransferIn: boolean): any {
        if (err === INSUFFICIENT_BALANCE) {
            return isTransferIn ?
                   new WalletErrors.InsufficientEntityBalanceError() :
                   new WalletErrors.InsufficientBalanceError();
        }

        if (err === BAD_TRANSACTION_ID) {
            return new WalletErrors.BadTransactionId();
        }

        if (err === MAX_CAPACITY_REACHED) {
            return new Errors.MaxCapacityReached();
        }
    }

    private isKnownWalletError(err): boolean {
        return err === INSUFFICIENT_BALANCE || err === BAD_TRANSACTION_ID || err === MAX_CAPACITY_REACHED;
    }

    private mapPaymentDeclineReason(err, isTransferIn: boolean = true): PaymentDeclineReason {
        if (err === INSUFFICIENT_BALANCE) {
            return isTransferIn ?
                   PaymentDeclineReason.INSUFFICIENT_ENTITY_BALANCE : PaymentDeclineReason.INSUFFICIENT_BALANCE;
        }
        if (err === BAD_TRANSACTION_ID) {
            return PaymentDeclineReason.BAD_TRANSACTION_ID;
        }
        return PaymentDeclineReason.INTERNAL_ERROR;
    }

    @auditAsync({ summary: "Payment status update: mark order as declined" })
    private async markOrderAsDeclined(@auditParam({ name: "payment", extractor: paymentAuditExtractor(DECLINED) })
                                          paymentData: PaymentDBInstance,
                                      @auditParam({ name: "declineReason" })
                                          declineReason: PaymentDeclineReason): Promise<void> {
        try {
            paymentData.set("orderStatus", DECLINED);
            paymentData.set("declineReason", declineReason);
            await paymentData.save();
        } catch (err) {
            log.error(err, "Failed to set Declined status for payment", paymentData.get("extTrxId"));
        }
    }

    private async validatePlayerAndGetPaymentData(brand: BrandEntity,
                                                  data: TransferData,
                                                  transferType: "transfer_in" | "transfer_out"
    ): Promise<{ player: Player, paymentData: PaymentDBInstance, isNew: boolean }> {

        const player: Player = await this.getAndValidatePlayer(brand, data);
        const findPaymentResult = await this.getOrCreatePaymentOrderDBItem(data, brand, player, transferType);

        if (transferType === TRANSFER_OUT) {
            await this.validateTransferOutsForOnlinePlayers(brand, player);
        }

        // isNew required for determine response code. as it's not a part of model. need to set it separately
        const isNew: boolean = findPaymentResult.isNew;
        const paymentData = findPaymentResult.dbInstance;
        return { player, isNew, paymentData };
    }

    private async validateTransferOutsForOnlinePlayers(brand: BrandEntity, player: Player) {
        const settings: EntitySettings = await getEntitySettings(brand.path);
        if (settings && settings.disableTransferOutForOnlinePlayers) {
            await this.ensurePlayerIsOffline(player);
        }
    }

    public async ensurePlayerIsOffline(player: Player) {
        if (player) {
            const playerInfo = await player.toInfoWithBalances();
            if (playerInfo.isOnline) {
                throw new Errors.PlayerIsOnline();
            }
        }
    }
}

function paymentAuditExtractor(status) {
    return (item) => ({ id: item.get("id"), updateStatus: status });
}

export async function getPaymentsList(entity: BrandEntity, filter: WhereOptions<any>): Promise<OrderInfo[]> {
    filter["brandId"] = entity.id.toString();

    const sortBy = FilterService.getSortKey(filter, sortableKeys, DEFAULT_SORT_KEY);
    const sortOrder = FilterService.valueFromQuery(filter, "sortOrder") || "DESC";
    const payments = await executeLongQuery(sequelizeSlave, async (trx) => {
        return PaymentOrderSlaveModel.findAll({
            where: filter,
            offset: FilterService.valueFromQuery(filter, "offset"),
            limit: FilterService.valueFromQuery(filter, "limit"),
            order: literal(`"${sortBy}" ${sortOrder}`),
            transaction: trx
        });
    });

    const result: OrderInfo[] = [];
    for (const payment of payments) {
        let info: OrderInfo;
        if (payment.get("orderStatus") === INIT) {
            // get fresh payment order info with actual transaction status
            info = await getEntityPlayerFinanceService()
                .checkStatusAndGetPaymentOrderById(payment.get("id"));
        } else {
            info = new PaymentOrderImpl(payment, entity).toInfo();
        }
        result.push(info);
    }

    return result;
}

export const systemFinanceService: Lazy<Promise<EntityPlayerFinanceService>> = lazy(
    async () => {
        const auditInfo = await masterAudit();
        auditInfo.request.swagger.operation.tags = ["Payment"];
        auditInfo.request.method = ACTION_METHOD.CRON;
        return getEntityPlayerFinanceService(auditInfo);
    });

export async function updatePaymentStatuses(): Promise<void> {
    const financeService = await systemFinanceService.get();

    const filter: WhereOptions<any> = {
        orderStatus: INIT
    };

    const batchSize = config.paymentStatusJob.batchSize;
    let batchOffset = 0;
    let payments: PaymentDBInstance[];

    do {
        payments = await PaymentOrderSlaveModel.findAll({
            where: filter,
            offset: batchOffset,
            limit: batchSize,
            order: literal("\"createdAt\" ASC"),
        });
        batchOffset += batchSize;

        for (const payment of payments) {
            await financeService.checkStatusAndGetPaymentOrderById(payment.get("id"));
        }
    } while (payments.length);
}

async function getBrand(entity: BaseEntity, path: string): Promise<BrandEntity> {
    let brandPath = path.trim();
    if (!brandPath.endsWith(PATH_CHAR)) {
        brandPath += PATH_CHAR;
    }
    return findBrandEntity(entity, { path: entity.path + brandPath });
}

let job: CronJob;

export function initPaymentStatusJob() {
    if (!job) {
        job = new CronJob({
            name: "payments",
            schedule: config.paymentStatusJob.schedule,
            timeout: config.paymentStatusJob.timeout
        }, updatePaymentStatuses);
    }
}
