import { suite, test, timeout } from "mocha-typescript";
import { expect, should, use } from "chai";
import * as chaiAsPromised from "chai-as-promised";
import { FACTORY } from "../../factories/common";
import { factory } from "factory-girl";
import { truncate } from "../../entities/helper";
import { BaseEntity } from "../../../skywind/entities/entity";
import { LimitLevel } from "../../../skywind/models/limitLevels";
import { SchemaConfiguration } from "../../../skywind/models/schemaConfiguration";
import { EntityGame, Game } from "../../../skywind/entities/game";
import { getNewLimitsFacade } from "../../../skywind/services/gameLimits/limitsFacade";
import { getGameLimitsConfigurationService } from "../../../skywind/services/gameLimits/gameLimitsConfiguration";
import { limitLevelService } from "../../../skywind/services/gameLimits/limitLevels";
import { ValidationError } from "../../../skywind/errors";
import { getGameLimitLevelService } from "../../../skywind/services/gameLimits/entityLimitLevels";
import { findOne } from "../../../skywind/services/entity";
import { SchemaDefinition } from "../../../skywind/entities/schemaDefinition";

@suite("Live limits", timeout(20000))
class LiveLimitsSpec {
    public static parent: BaseEntity;
    public static game: Game;
    public static parentGame: EntityGame;
    public static masterGame: EntityGame;
    public static configuration: SchemaConfiguration;
    public static definition: SchemaDefinition;
    public static highLevel: LimitLevel;
    public static midLevel: LimitLevel;
    public static lowLevel: LimitLevel;

    public static async before() {
        should();
        use(chaiAsPromised);
        await truncate(true);

        LiveLimitsSpec.definition = await factory.create(FACTORY.SCHEMA_DEFINITION_LIVE);

        const [high, mid, low] = await factory.createMany(FACTORY.LIMIT_LEVEL, 3);
        LiveLimitsSpec.highLevel = high.toInfo(true);
        LiveLimitsSpec.midLevel = mid.toInfo(true);
        LiveLimitsSpec.lowLevel = low.toInfo(true);

        LiveLimitsSpec.parent = await factory.create(FACTORY.ENTITY, {}, { currencies: ["EUR", "USD", "CNY"] });
        LiveLimitsSpec.game = await factory.create(FACTORY.GAME, {},
            { features: { live: {} }, schemaDefinitionId: LiveLimitsSpec.definition.id });

        LiveLimitsSpec.masterGame = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameId: LiveLimitsSpec.game.id,
            entityId: 1
        });

        LiveLimitsSpec.parentGame = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameId: LiveLimitsSpec.game.id,
            parentEntityGameId: LiveLimitsSpec.masterGame.id,
            entityId: LiveLimitsSpec.parent.id
        });
        const master = await findOne({ path: ":" });

        await getGameLimitsConfigurationService(master, true).create(master, {
            schemaDefinitionId: LiveLimitsSpec.definition.id,
            gameCode: LiveLimitsSpec.game.code,
            gameLimits: {
                "EUR": {
                    [high.pid]: {
                        "bets": {
                            "playerPair": { "max": 1000, "exposure": 600000, "min": 5, payout: 12 },
                            "egalite": { "max": 50, "exposure": 30000, "min": 5, payout: 221 },
                            "big": { "max": 500, "exposure": 300000, "min": 5, payout: 1.54 }
                        },
                        "totalStakeMin": 5,
                        "defaultTotalStake": 10,
                        "stakeDef": 10,
                        "totalStakeMax": 10000,
                        "stakeAll": [1, 3, 5, 10, 25, 50, 100, 500, 1000, 2500, 5000, 10000],
                        concurrentPlayers: 100
                    },
                    [low.pid]: {
                        "bets": {
                            "playerPair": { "max": 500, "exposure": 300000, "min": 1, payout: 12 },
                            "egalite": { "max": 25, "exposure": 15000, "min": 1, payout: 221 },
                            "big": { "max": 250, "exposure": 150000, "min": 1, payout: 1.54 }
                        },
                        "totalStakeMin": 1,
                        "stakeDef": 1,
                        "defaultTotalStake": 1,
                        "totalStakeMax": 2500,
                        "stakeAll": [1, 3, 5, 10, 25, 50, 100],
                        "concurrentPlayers": 100,
                        isDefaultRoom: true
                    }
                }
            },
            levels: [low.pid, high.pid]
        } as any);

    }

    @test
    public async testValidationMasterConfigurationExists() {
        const definition = await factory.create(FACTORY.SCHEMA_DEFINITION_LIVE);
        const game = await factory.create(FACTORY.GAME, {},
            { features: { live: {} }, schemaDefinitionId: definition.id });

        const masterGame = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameId: game.id,
            entityId: 1
        });

        const entity = await factory.create(FACTORY.ENTITY);
        await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameId: game.id,
            parentEntityGameId: masterGame.id,
            entityId: entity.id
        });

        await getGameLimitsConfigurationService(entity).create(entity, {
            schemaDefinitionId: definition.id,
            gameCode: game.code,
            gameLimits: {
                "EUR": {
                    [LiveLimitsSpec.highLevel.pid]: {
                        "bets": {
                            "playerPair": { "max": 1000, "exposure": 600000, "min": 5, payout: 12 },
                            "egalite": { "max": 50, "exposure": 30000, "min": 5, payout: 221 },
                            "big": { "max": 500, "exposure": 300000, "min": 5, payout: 1.54 }
                        },
                        "totalStakeMin": 5,
                        "defaultTotalStake": 10,
                        "stakeDef": 10,
                        "totalStakeMax": 10000,
                        "stakeAll": [1, 3, 5, 10, 25, 50, 100, 500, 1000, 2500, 5000, 10000],
                        concurrentPlayers: 100
                    }
                }
            },
            levels: [LiveLimitsSpec.highLevel.pid]
        } as any).should.eventually.rejectedWith(ValidationError);
    }

    @test
    public async testDefaultLevel() {
        const child = await factory.create(FACTORY.BRAND, {},
            { currencies: ["EUR", "USD", "CNY"], parent: LiveLimitsSpec.parent });

        const game = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameId: LiveLimitsSpec.game.id,
            parentEntityGameId: LiveLimitsSpec.parentGame.id,
            entityId: child.id,
        });

        await factory.create(FACTORY.ENTITY_GAME_LIMIT_LEVEL, {},
            {
                levelId: LiveLimitsSpec.highLevel.id,
                gameCode: LiveLimitsSpec.game.code,
                entityId: child.id,
                isDefault: true
            });

        const limits = await getNewLimitsFacade(child)
            .buildGameLaunch({
                game: {
                    code: LiveLimitsSpec.game.code,
                    totalBetMultiplier: 10,
                    schemaDefinitionId: LiveLimitsSpec.definition.id
                } as any,
                currency: "USD"
            });

        expect(Object.keys(limits).length).to.be.equal(2);
        expect(limits[LiveLimitsSpec.highLevel.title].isDefaultRoom).to.be.true;
    }

    @test
    public async testHideLevelInChild() {
        const child = await factory.create(FACTORY.BRAND, {},
            { currencies: ["EUR", "USD", "CNY"], parent: LiveLimitsSpec.parent });

        const game = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameId: LiveLimitsSpec.game.id,
            parentEntityGameId: LiveLimitsSpec.parentGame.id,
            entityId: child.id
        });

        await factory.create(FACTORY.ENTITY_GAME_LIMIT_LEVEL, {},
            {
                levelId: LiveLimitsSpec.lowLevel.id,
                gameCode: LiveLimitsSpec.game.code,
                entityId: child.id,
                hidden: true
            });
        await factory.create(FACTORY.ENTITY_GAME_LIMIT_LEVEL, {},
            {
                levelId: LiveLimitsSpec.highLevel.id,
                gameCode: LiveLimitsSpec.game.code,
                entityId: child.id,
                isDefault: true,
                hidden: false,
                currency: null
            });

        const limits = await getNewLimitsFacade(child)
            .buildGameLaunch({
                game: {
                    code: LiveLimitsSpec.game.code,
                    totalBetMultiplier: 10,
                    schemaDefinitionId: LiveLimitsSpec.definition.id
                } as any,
                currency: "CNY"
            });

        expect(Object.keys(limits).length).to.be.equal(1);
        expect(limits[LiveLimitsSpec.highLevel.title].isDefaultRoom).to.be.true;
    }

    @test
    public async testTryToDeleteLevelWhichInUse() {
        const superHigh = await factory.create(FACTORY.LIMIT_LEVEL, {}, { entityId: LiveLimitsSpec.parent.id });

        const child = await factory.create(FACTORY.BRAND, {},
            { currencies: ["EUR", "USD", "CNY"], parent: LiveLimitsSpec.parent });

        const game = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameId: LiveLimitsSpec.game.id,
            parentEntityGameId: LiveLimitsSpec.parentGame.id,
            entityId: child.id
        });

        await getGameLimitsConfigurationService(child).create(child, {
            schemaDefinitionId: LiveLimitsSpec.definition.id,
            gameLimits: {
                "EUR": {
                    [superHigh.pid]: {
                        "bets": {
                            "playerPair": { "max": 1000, "exposure": 600000, "min": 5, payout: 12 },
                            "egalite": { "max": 50, "exposure": 30000, "min": 5, payout: 221 },
                            "big": { "max": 500, "exposure": 300000, "min": 5, payout: 1.54 }
                        },
                        "totalStakeMin": 5,
                        "defaultTotalStake": 10,
                        "stakeDef": 10,
                        "totalStakeMax": 10000,
                        "stakeAll": [1, 3, 5, 10, 25, 50, 100, 500, 1000, 2500, 5000, 10000],
                        concurrentPlayers: 100
                    }
                }
            },
            levels: [superHigh.pid],
            gameCode: game.game.code
        });

        await limitLevelService.get().destroy(superHigh.id).should.eventually.rejectedWith(ValidationError)
            .then(err => expect(err.message).to.be.equal("Validation error: Level is used in game-limits"));

        const result = await getGameLimitLevelService(child, true).getGameLimitLevels({ gameCode: game.game.code });

        expect(result.length).to.be.equal(1);
        expect(result[0].isDefault).to.be.false;
        expect(result[0].hidden).to.be.false;
    }

    @test
    public async testRecreateEntityLevelRelation() {
        const superHigh = await factory.create(FACTORY.LIMIT_LEVEL, {}, { entityId: LiveLimitsSpec.parent.id });

        const child = await factory.create(FACTORY.BRAND, {},
            { currencies: ["EUR", "USD", "CNY"], parent: LiveLimitsSpec.parent });

        const game = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameId: LiveLimitsSpec.game.id,
            parentEntityGameId: LiveLimitsSpec.parentGame.id,
            entityId: child.id
        });

        const data = {
            schemaDefinitionId: LiveLimitsSpec.definition.id,
            gameLimits: {
                "EUR": {
                    [superHigh.pid]: {
                        "bets": {
                            "playerPair": { "max": 1000, "exposure": 600000, "min": 5, payout: 12 },
                            "egalite": { "max": 50, "exposure": 30000, "min": 5, payout: 221 },
                            "big": { "max": 500, "exposure": 300000, "min": 5, payout: 1.54 }
                        },
                        "totalStakeMin": 5,
                        "defaultTotalStake": 10,
                        "stakeDef": 10,
                        "totalStakeMax": 10000,
                        "stakeAll": [1, 3, 5, 10, 25, 50, 100, 500, 1000, 2500, 5000, 10000],
                        concurrentPlayers: 100
                    }
                }
            },
            levels: [superHigh.pid],
            gameCode: game.game.code
        };

        const { id } = await getGameLimitsConfigurationService(child).create(child, data);

        const gameLimitLevelsAfterCreate = await getGameLimitLevelService(child, true)
            .getGameLimitLevels({ gameCode: game.game.code });

        await getGameLimitsConfigurationService(child).update(child, id, data);

        const gameLimitLevelsAfterUpdate = await getGameLimitLevelService(child, true)
            .getGameLimitLevels({ gameCode: game.game.code });

        expect(gameLimitLevelsAfterUpdate.length).to.be.equal(1);
        expect(gameLimitLevelsAfterUpdate[0].id !== gameLimitLevelsAfterCreate[0].id).to.be.true;
    }

    @test
    public async testCannotHideLastLevelForChild() {
        const superHigh = await factory.create(FACTORY.LIMIT_LEVEL, {}, { entityId: 1 });
        const brand = await factory.create(FACTORY.BRAND, {},
            { currencies: ["EUR", "USD", "CNY"] });

        const game = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameId: LiveLimitsSpec.game.id,
            parentEntityGameId: LiveLimitsSpec.masterGame.id,
            entityId: brand.id
        });

        const dataForHideInChild = [
            {
                levelId: LiveLimitsSpec.midLevel.id,
                hidden: true,
                gameCode: game.game.code
            },
            {
                levelId: LiveLimitsSpec.highLevel.id,
                hidden: true,
                gameCode: game.game.code
            },
            {
                levelId: LiveLimitsSpec.lowLevel.id,
                hidden: true,
                gameCode: game.game.code
            }
        ];

        for (const customization of dataForHideInChild) {
            await getGameLimitLevelService(brand).create(customization);
        }

        const master = await findOne({ id: 1 });
        await getGameLimitLevelService(master)
            .create({ hidden: true, levelId: superHigh.id, gameCode: game.game.code })
            .should.eventually.rejectedWith(ValidationError);
    }

    @test
    public async testCannotCreateNewLevelWithEmptyBets() {
        const newLevel = await factory.create(FACTORY.LIMIT_LEVEL, {}, { entityId: LiveLimitsSpec.parent.id });

        const child = await factory.create(FACTORY.BRAND, {},
            { currencies: ["EUR", "USD", "CNY"], parent: LiveLimitsSpec.parent });

        const game = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameId: LiveLimitsSpec.game.id,
            parentEntityGameId: LiveLimitsSpec.parentGame.id,
            entityId: child.id
        });

        await getGameLimitsConfigurationService(child).create(child, {
            schemaDefinitionId: LiveLimitsSpec.definition.id,
            gameLimits: {
                "EUR": {
                    [newLevel.pid]: {
                        "bets": {},
                        "totalStakeMin": 5,
                        "defaultTotalStake": 10,
                        "stakeDef": 10,
                        "totalStakeMax": 10000,
                        "stakeAll": [1, 3, 5, 10, 25, 50, 100, 500, 1000, 2500, 5000, 10000],
                        concurrentPlayers: 100
                    }
                }
            },
            levels: [newLevel.pid],
            gameCode: game.game.code
        }).should.be.rejectedWith(ValidationError).then(err => {
            expect(err.message).to.be.equal("Validation error: Bets should be non-empty object");
        });
    }

    @test
    public async testCreateCustomLevel() {
        const newLevel = await factory.create(FACTORY.LIMIT_LEVEL, {}, { entityId: LiveLimitsSpec.parent.id });

        const child = await factory.create(FACTORY.BRAND, {},
            { currencies: ["EUR", "USD", "CNY"], parent: LiveLimitsSpec.parent });

        const game = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameId: LiveLimitsSpec.game.id,
            parentEntityGameId: LiveLimitsSpec.parentGame.id,
            entityId: child.id
        });

        const result = await getGameLimitsConfigurationService(child).create(child, {
            schemaDefinitionId: LiveLimitsSpec.definition.id,
            gameLimits: {
                "EUR": {
                    [newLevel.pid]: {
                        "bets": {
                            "playerPair": { "max": 1000, "exposure": 600000, "min": 5, payout: 12 },
                            "egalite": { "max": 50, "exposure": 30000, "min": 5, payout: 221 },
                            "big": { "max": 500, "exposure": 300000, "min": 5, payout: 1.54 }
                        },
                        "totalStakeMin": 5,
                        "defaultTotalStake": 10,
                        "stakeDef": 10,
                        "totalStakeMax": 10000,
                        "stakeAll": [1, 3, 5, 10, 25, 50, 100, 500, 1000, 2500, 5000, 10000],
                        concurrentPlayers: 100
                    }
                },
                "CNY": {
                    [newLevel.pid]: {
                        "bets": {
                            "playerPair": { "max": 1000, "exposure": 600000, "min": 10, payout: 12 },
                            "egalite": { "max": 50, "exposure": 30000, "min": 10, payout: 221 },
                            "big": { "max": 500, "exposure": 300000, "min": 10, payout: 1.54 }
                        },
                        "totalStakeMin": 5,
                        "defaultTotalStake": 10,
                        "stakeDef": 10,
                        "totalStakeMax": 10000,
                        "stakeAll": [1, 3, 5, 10, 25, 50, 100, 500, 1000, 2500, 5000, 10000],
                        concurrentPlayers: 100
                    }
                }
            },
            levels: [newLevel.pid, LiveLimitsSpec.highLevel.pid],
            gameCode: game.game.code
        });

        expect(result.id).to.be.exist;
    }
}
