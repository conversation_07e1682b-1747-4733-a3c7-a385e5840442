######################### SONAR STUFF #########################
FROM asia.gcr.io/gcpstg/lerna-builder:node_22 as sonar

WORKDIR /app
COPY . /app/

RUN npm install \
    && npm run clean \
    && npm run compile \
    && npm run version \
    && rm .npmrc
CMD ["/bin/cat", "/app/out/skywind/version"]
CMD ["node", "/app/out/skywind/appGameProvider.js"]

######################### MAIN IMAGE #########################
FROM node:22.14.0-alpine as main

#Using /sbin/nologin, because alpine image does not support /usr/sbin/nologin as mentioned in sysusers/gpapi.json
RUN mkdir -p /opt/gpapi \
    && adduser -u 2003 -h /opt/gpapi -Ds /sbin/nologin gpapi

EXPOSE 3000

WORKDIR /app

COPY --from=sonar /app .
RUN rm -rf src coverage

RUN npm prune --omit=dev && npm cache clean --force && rm -f .npmrc

CMD ["node", "/app/out/skywind/appGameProvider.js"]
