import config from "./config";
import * as fastify from "fastify";
import { FastifyInstance, FastifyReply, FastifyRequest } from "fastify";
import compression from "@fastify/compress";
import * as cookie from "@fastify/cookie";
import * as methodOverride from "method-override";
import { measures, logging } from "@skywind-group/sw-utils";
import { buildLogData, findIp, getRequestInfoFomRequest, IpHolder } from "./utils/requestHelper";
import { GameProviderAPIErrors } from "./errors";
import Translation from "@skywind-group/sw-management-i18n";
import Logger = logging.Logger;

export const SITE_COOKIE = "siteRefCookie";

export interface WithCookies {
    cookies: any;
}

export type Request = WithCookies & IpHolder;
export type Response = FastifyReply;

export async function create(): Promise<fastify.FastifyInstance> {
    const app: fastify.FastifyInstance = fastify({
        bodyLimit: config.bodyParserJsonLimit
    });
    app.register(cookie);
    app.register(compression, { threshold: config.compressionThreshold });
    app.register(methodOverride());

   app.addHook("onRequest", async (req, reply) => {
        Translation.middleware(req, reply, undefined);
    });
    app.addHook(
        "onRequest",
        (req: FastifyRequest, rep: FastifyReply, done: () => void) => allowHTTPMethods(req, rep, done)
    );
    setUpMetricHandlers(app);

    return app;
}

function isPrometheusMonitoring() {
    return measures.providerName === "prometheus";
}

export function setUpMetricHandlers(app: FastifyInstance) {
    if (isPrometheusMonitoring()) {
        app.get("/metrics", async (req, res) => {
            return res.code(200).type("text/plain").send(await measures.measureProvider.getMeasuresStream());
        });
    }
}

export function resolveReferrer(req: Request) {
    return req.cookies[SITE_COOKIE];
}

export function createRequestLogger(logger: Logger) {
    return async (req: FastifyRequest & Request, res: FastifyReply): Promise<void> => {
        if (req.url.endsWith("version") || req.url.endsWith("health")) {
            return;
        }
        const reqData = buildLogData(
            req.url,
            req.method,
            req.query,
            req.body,
            getRequestInfoFomRequest(req)
        );
        const method = (req.url && req.url.includes("/transactionId")) ? "debug" : "info";
        logger[method]({ reqData: reqData }, "Http request");
    };
}

export async function addHeaderCacheControl(req: FastifyRequest & Request, res: FastifyReply): Promise<void> {
    if (req.method === "GET") {
        res.header("Cache-Control", "no-cache, max-age=0");
    }
}

export function resolveIp(req: FastifyRequest & Request, res: Response, next) {
    req.resolvedIp = findIp(req);
    next();
}

export function notFoundHandler(req: FastifyRequest & Request, res: Response) {
    const error = new GameProviderAPIErrors.ApiNotFoundError();
    res.code(error.responseStatus).send({
        code: error.code,
        message: error.message,
    });
}

export function allowHTTPMethods(request: FastifyRequest,
                                 reply: FastifyReply,
                                 done: () => void) {
    const allowedMethods = new Set(config.allowedHTTPMethods);
    const method = request.method.toUpperCase();
    reply.header("Access-Control-Allow-Methods", config.allowedHTTPMethods.join(","));
    if (!allowedMethods.has(method)) {
        reply.code(405).send({
            statusCode: 405,
            message: `${method} not allowed.`,
            error: "Method Not Allowed"
        })
    }
    done();
}
