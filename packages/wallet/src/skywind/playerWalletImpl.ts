import { DepositData, GamePaymentData, PlayerWallet, RedeemData, WithdrawalData } from "./playerWallet";
import { IAccount, IChangeInfo, ITransaction, IWallet } from "@skywind-group/sw-wallet";
import { RemoteWalletFacade, WalletFacade } from "./walletFacade";
import { Currencies, Currency } from "@skywind-group/sw-currency-exchange";
import { ONLY_BALANCE_FILTER, PLAYER, WALLET_TRX_TYPE } from "./common";
import { AccountPropertiesFilter } from "./filters";
import { PlayerRealBalance, PlayerResultBalance } from "./balance";
import { PlayerBalanceProvider } from "./playerBalanceService";
import { WalletErrors } from "./errors";
import { getAsyncLocalWalletStore } from "./asyncLocalWallet";
import { logging } from "@skywind-group/sw-utils";

const log = logging.logger("player-wallet");

export class PlayerWalletImpl implements PlayerWallet, PlayerBalanceProvider<PlayerRealBalance> {
    public readonly walletKey: string;
    public readonly currency: Currency;

    constructor(private readonly brandId: number,
                private readonly playerCode: string,
                public readonly currencyCode: string) {
        this.walletKey = PlayerWalletImpl.toWalletKey(brandId, playerCode, currencyCode);
        this.currency = Currencies.get(this.currencyCode);
    }

    public async getBalance(): Promise<PlayerRealBalance> {
        return this.getPlayerBalance(ONLY_BALANCE_FILTER[0]);
    }

    public async getBalances(): Promise<{ [currency: string]: PlayerRealBalance }> {
        return { [this.currencyCode]: await this.getBalance() };
    }

    public async getPlayerBalance(filter: AccountPropertiesFilter): Promise<PlayerRealBalance | undefined> {
        if (filter.account === PLAYER.PLAYER_MAIN_ACCOUNT || filter.account === PLAYER.PLAYER_RT_ACCOUNT) {
            const playerWallet: IWallet = await this.getWallet();
            const account = playerWallet.accounts.get(filter.account);
            const result: any = {};
            filter.properties.forEach((property) => {
                const amount = account.get(property.name) as number;
                if (property.name === PLAYER.PLAYER_BALANCE) {
                    result.main = amount ? this.currency.toMajorUnits(amount) : 0;
                } else {
                    if (amount) {
                        if (!result.extraBalances) {
                            result.extraBalances = {};
                        }
                        result.extraBalances[property.name] = property.fromWalletValue(amount);
                    }
                }
            });

            return result;
        }
    }

    public async getWallet(trx?: ITransaction, useRemoteWallet?: boolean): Promise<IWallet> {
        const wallet = getAsyncLocalWalletStore()?.wallet;
        log.debug({ wallet }, "Retrieve local wallet");
        const walletFacade = useRemoteWallet ? RemoteWalletFacade : WalletFacade;
        if (wallet) {
            const walletKey = this.getWalletKey(this.currencyCode);
            const createdWallet = await walletFacade.create(
                walletKey,
                wallet,
                trx?.changes
            );
            if (trx) {
                await trx.setWallet(walletKey, createdWallet);
            }
            return createdWallet;
        }
        return trx ?
               trx.getWallet(this.getWalletKey(this.currencyCode)) :
               await walletFacade.get(this.getWalletKey(this.currencyCode));
    }

    public async deposit(trx: ITransaction, data: DepositData): Promise<void> {
        if (this.currencyCode !== data.currency) {
            return Promise.reject(new WalletErrors.PlayerCurrencyNotMatchError(data.currency));
        }
        if (data.amount <= 0) {
            return Promise.reject(new WalletErrors.AmountIsNegativeError());
        }
        return this.incrementBalance(trx, data.currency, data.amount, WALLET_TRX_TYPE.DEPOSIT);
    }

    public async withdraw(trx: ITransaction, data: WithdrawalData): Promise<void> {
        if (this.currencyCode !== data.currency) {
            return Promise.reject(new WalletErrors.PlayerCurrencyNotMatchError(data.currency));
        }

        if (data.amount <= 0) {
            return Promise.reject(new WalletErrors.AmountIsNegativeError());
        }
        return this.incrementBalance(trx, data.currency, -data.amount, WALLET_TRX_TYPE.WITHDRAW);
    }

    public async commitGamePayment(trx: ITransaction, data: GamePaymentData): Promise<void> {
        if (this.currencyCode !== data.currency) {
            return Promise.reject(new WalletErrors.PlayerCurrencyNotMatchError(data.currency));
        }
        if (data.bet < 0 || data.win < 0) {
            return Promise.reject(new WalletErrors.AmountIsNegativeError());
        }
        const playerWallet: IWallet = await trx.getWallet(this.getWalletKey(data.currency));
        const account: IAccount = playerWallet.accounts.get(PLAYER.PLAYER_MAIN_ACCOUNT);
        const walletCurrency = Currencies.get(data.currency);

        account.inc(PLAYER.PLAYER_BALANCE,
            data.freeBetCoin ? 0 : -walletCurrency.toMinorUnits(data.bet || 0),
            WALLET_TRX_TYPE.BET,
            0);

        account.inc(PLAYER.PLAYER_BALANCE,
            walletCurrency.toMinorUnits(data.win || 0),
            WALLET_TRX_TYPE.WIN,
            0);
    }

    public async getResultBalance(trxResult: IChangeInfo[], currency: string): Promise<PlayerRealBalance> {
        const walletCurrency = Currencies.get(currency);

        const state = PlayerWalletImpl.parseTrxResult(this.walletKey, trxResult);

        if (state.currentValue === undefined) {
            return this.getBalance();
        }

        return {
            main: walletCurrency.toMajorUnits(state.currentValue),
            previousValue: walletCurrency.toMajorUnits(state.prevValue),
        };
    }

    public async redeem(trx: ITransaction, data: RedeemData): Promise<void> {
        const wallet: IWallet = await this.getWallet(trx);

        const account: IAccount = wallet.accounts.get(PLAYER.PLAYER_MAIN_ACCOUNT);
        const amount = Currencies.get(this.currencyCode).toMinorUnits(data.redeemBalance);
        account.inc(PLAYER.PLAYER_BALANCE, amount, data.type);
    }

    private async incrementBalance(trx: ITransaction, currency: string, amount: number, txType: string): Promise<void> {
        const wallet: IWallet = await trx.getWallet(this.getWalletKey(currency));
        const account: IAccount = wallet.accounts.get(PLAYER.PLAYER_MAIN_ACCOUNT);
        const walletCurrency = Currencies.get(currency);
        account.inc(PLAYER.PLAYER_BALANCE, walletCurrency.toMinorUnits(amount), txType);
    }

    public getWalletKey(currency: string) {
        return PlayerWalletImpl.toWalletKey(this.brandId, this.playerCode, currency);
    }

    public static toWalletKey(brandId: number, playerCode: string, currency: string): string {
        return PLAYER.PLAYER_WALLET_PREFIX + ":" + brandId + ":" + playerCode + ":" + currency;
    }

    public static fromWalletKey(walletKey: string) {
        const data = walletKey.split(":");
        return [data[1], data[2], data[3]];
    }

    public static getMainAccountKey(): string {
        return PLAYER.PLAYER_MAIN_ACCOUNT;
    }

    public static getWalletPerGameAccount(gameCode: string, deviceId: string = "web"): string {
        return `${gameCode}%${deviceId}`;
    }

    public static parseTrxResult(walletKey: string,
                                 trxResult: IChangeInfo[],
                                 accountKey = PLAYER.PLAYER_MAIN_ACCOUNT): PlayerResultBalance {

        let prevValue: number;
        let currentValue: number;
        let i = 0;

        // the fist change in the list contains previous value
        for (; i < trxResult.length; i++) {
            if (PlayerWalletImpl.isPlayerBalanceChange(walletKey, trxResult[i], accountKey)) {
                prevValue = +trxResult[i].prevValue;
                break;
            }
        }

        // the last change in the list contains current value
        for (let j = trxResult.length - 1; j >= i; j--) {
            if (PlayerWalletImpl.isPlayerBalanceChange(walletKey, trxResult[j], accountKey)) {
                currentValue = +trxResult[j].value;
                break;
            }
        }

        return { prevValue, currentValue };
    }

    private static isPlayerBalanceChange(key: string, ci: IChangeInfo, accountKey: string) {
        return ci.walletKey === key
            && ci.account === accountKey
            && ci.property === PLAYER.PLAYER_BALANCE;
    }

}
