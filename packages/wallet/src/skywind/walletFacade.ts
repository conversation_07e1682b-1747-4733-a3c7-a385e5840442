import {
    BAD_TRANSACTION_ID,
    connect,
    IExternalTransaction,
    IOperation,
    ITransaction,
    ITrxData,
    ITrxId,
    IWallet,
    IWalletConfiguration,
    IWalletManager,
    TRANSACTION_EXISTS,
    TRANSACTION_IS_PROCESSING,
    RawWallet,
    IChanges, IHttpWalletConfiguration
} from "@skywind-group/sw-wallet";
import { OPERATION_ID } from "./common";
import { WalletErrors } from "./errors";

export enum SaveTransactionResult {
    SAVED,
    EXISTS
}

export interface IWalletFacade {

    initWalletManager(config: IWalletConfiguration);

    get(key: string): Promise<IWallet>;

    startTransactionWithID(id: string | ITrxId, operation?: IOperation): Promise<ITransaction>;

    startTransaction(): Promise<ITransaction>;

    generateTransactionId(): Promise<string>;

    generateTrxId(): Promise<ITrxId>;

    parseTransactionId(trxId: string): Promise<ITrxId>;

    saveExternalTransaction(transaction: IExternalTransaction,
                            ts?: string): Promise<SaveTransactionResult>;

    logExternalTransaction(transaction: IExternalTransaction,
                           ts?: string): Promise<any>;

    findCommittedTransaction(transactionId: string | ITrxId,
                             operationId: OPERATION_ID): Promise<ITrxData>;

    getManager(): Promise<IWalletManager>;
}

class WalletFacadeImpl implements IWalletFacade {
    private walletManager: IWalletManager;
    private walletConfig: IWalletConfiguration;

    public initWalletManager(config: IWalletConfiguration) {
        if (this.walletConfig) {
            throw new Error("IWalletManager has been initialized already");
        }
        this.walletConfig = config;
    }

    /**
     * getManager - singleton which return the current wallet management access
     * @returns {any}
     */
    public async getManager(): Promise<IWalletManager> {
        if (!this.walletManager) {
            const walletConfig = this.getWalletConfig();
            if (walletConfig.type === "http" && !(walletConfig as IHttpWalletConfiguration).baseURL) {
                throw new Error("baseURL is required for the http wallet");
            }
            this.walletManager = await connect(walletConfig);
        }
        return this.walletManager;
    }

    /**
     * get - get specific wallet account
     *
     * @param key - unique key of the wallet account
     * @returns {Promise<IWallet>}
     */
    public async get(key: string): Promise<IWallet> {
        const manager = await this.getManager();
        return manager.get(key);
    }

    /**
     * create - create wallet account from raw wallet data
     *
     * @param key - unique key of the wallet account
     * @param rawWallet - raw wallet data
     * @param changes - account changes
     * @returns {Promise<IWallet>}
     */
    public async create(key: string, rawWallet: RawWallet, changes?: IChanges): Promise<IWallet> {
        const manager = await this.getManager();
        return manager.create(key, rawWallet, changes);
    }

    /**
     * getRawWallet - get raw wallet data from wallet account
     *
     * @param wallet - wallet account
     * @returns {Promise<IWallet>}
     */
    public async getRawWallet(wallet: IWallet): Promise<RawWallet> {
        const manager = await this.getManager();
        return manager.getRawWallet(wallet);
    }

    /**
     * start transaction
     */
    public async startTransactionWithID(id: string | ITrxId, operation?: IOperation): Promise<ITransaction> {
        const manager: IWalletManager = await this.getManager();
        const transactionId = id === undefined ? await manager.generateTransactionId() : id;
        return manager.startTransaction(transactionId, operation);
    }

    public async startTransaction(): Promise<ITransaction> {
        return this.startTransactionWithID(undefined);
    }

    public async generateTransactionId(): Promise<string> {
        return this.generateTrxId().then(trxId => trxId.publicId);
    }

    public async generateTrxId(): Promise<ITrxId> {
        const manager: IWalletManager = await this.getManager();
        return manager.generateTransactionId();
    }

    public async parseTransactionId(trxId: string): Promise<ITrxId> {
        const manager: IWalletManager = await this.getManager();
        return manager.parseTransactionId(trxId);
    }

    public async saveExternalTransaction(transaction: IExternalTransaction,
                                         ts?: string): Promise<SaveTransactionResult> {
        const manager: IWalletManager = await this.getManager();
        try {
            await manager.saveExternalTransaction(transaction, ts ? new Date(ts) : undefined);

            return SaveTransactionResult.SAVED;
        } catch (err) {
            if (err === TRANSACTION_EXISTS) {
                return SaveTransactionResult.EXISTS;
            }

            if (err === BAD_TRANSACTION_ID) {
                return Promise.reject(new WalletErrors.BadTransactionId());
            }

            if (err === TRANSACTION_IS_PROCESSING) {
                return Promise.reject(new WalletErrors.TransactionIsProcessing());
            }

            return Promise.reject(err);
        }
    }

    public async logExternalTransaction(transaction: IExternalTransaction,
                                        ts?: string): Promise<any> {
        if (this.supportWalletTrxExternalLog()) {
            const manager: IWalletManager = await this.getManager();
            return manager.logExternalTransaction(transaction, ts ? new Date(ts) : undefined);
        } else {
            return this.saveExternalTransaction(transaction, ts);
        }
    }

    public async findCommittedTransaction(transactionId: string | ITrxId,
                                          operationId: OPERATION_ID = OPERATION_ID.BET): Promise<ITrxData> {
        const manager: IWalletManager = await this.getManager();
        try {
            return await manager.findCommittedTransaction(transactionId, operationId);
        } catch (err) {
            if (err === TRANSACTION_IS_PROCESSING) {
                return Promise.reject(new WalletErrors.TransactionIsProcessing());
            }
            return Promise.reject(err);
        }
    }

    private supportWalletTrxExternalLog() {
        return !!this.walletConfig.externalTransactionLog;
    }

    private getWalletConfig() {
        if (!this.walletConfig) {
            throw new Error("IWalletManager is not initialized!");
        }

        return this.walletConfig;
    }

}

export const WalletFacade = new WalletFacadeImpl();
export const RemoteWalletFacade = new WalletFacadeImpl();
